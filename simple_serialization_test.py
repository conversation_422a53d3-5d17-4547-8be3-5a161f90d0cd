#!/usr/bin/env python3

print("测试序列化功能...")

try:
    from beezer.type_model import SiemensConfig, InboundTypes
    print("✅ 导入成功")
    
    # 创建配置对象
    config = SiemensConfig(
        type=InboundTypes.Siemens,
        id="test",
        model="828D",
        ip="*************",
        port=102,
        interval=1
    )
    print("✅ 配置对象创建成功")
    
    # 获取原始字典
    config_dict = config.model_dump()
    print(f"✅ 原始字典: {config_dict}")
    print(f"✅ type 类型: {type(config_dict['type'])}")
    print(f"✅ ip 类型: {type(config_dict['ip'])}")
    
    # 测试序列化函数
    from beezer.gui.config_manager import ConfigManager
    cm = ConfigManager()
    
    serialized = cm._serialize_config_for_yaml(config_dict)
    print(f"✅ 序列化后: {serialized}")
    print(f"✅ 序列化后 type 类型: {type(serialized['type'])}")
    print(f"✅ 序列化后 ip 类型: {type(serialized['ip'])}")
    
    # 检查值
    if isinstance(serialized['type'], str):
        print("✅ type 正确序列化为字符串")
    else:
        print("❌ type 未正确序列化")
        
    if isinstance(serialized['ip'], str):
        print("✅ ip 正确序列化为字符串")
    else:
        print("❌ ip 未正确序列化")
    
    print("✅ 测试完成")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
