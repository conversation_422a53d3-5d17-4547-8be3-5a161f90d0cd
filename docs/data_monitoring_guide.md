# Beezer 数据监控功能指南

## 概述

Beezer 数据监控功能通过修改 `report_status` 机制实现，可以实时监控每个插件的状态和数据流。该功能包括：

- 插件状态监控
- 数据流监控
- 实时GUI界面
- 运行时管理

## 架构设计

### 1. 数据上报机制

#### 状态上报
- 插件通过 `report_status()` 方法上报状态
- 支持状态类型：`running`, `idle`, `error`, `stopped`
- 包含详细信息：内存使用、CPU使用、最后活动时间等

#### 数据上报
- 插件通过 `report_data()` 方法上报数据
- 支持数据类型：`input`, `output`, `processed`
- 自动在数据处理关键点上报

### 2. API接口

#### 状态相关API
```
POST /api/plugin/status    # 上报插件状态
GET  /api/plugin/status    # 获取所有插件状态
GET  /api/plugin/status/<plugin_id>  # 获取指定插件状态
```

#### 数据相关API
```
POST /api/plugin/data      # 上报插件数据
GET  /api/plugin/data      # 获取所有插件数据
GET  /api/plugin/data/<plugin_id>    # 获取指定插件数据
```

### 3. GUI界面

#### 监控视图
- **插件状态标签页**：显示所有插件的运行状态
- **数据监控标签页**：实时显示数据流
- **统计卡片**：总插件数、运行中插件数、错误插件数、数据条目数

#### 运行时管理
- 启动/停止/重启 Beezer 服务
- 实时状态监控
- 错误信息显示

## 使用指南

### 1. 启动监控系统

#### 方法一：使用演示脚本
```bash
python start_monitor_demo.py
```

这会自动启动：
- Beezer 主服务 (端口 8000)
- GUI 界面 (端口 8081)
- 测试数据生成器

#### 方法二：手动启动
```bash
# 启动主服务
python -m beezer.main

# 启动GUI (新终端)
python -m beezer.gui

# 生成测试数据 (新终端)
python test_monitor_api.py
```

### 2. 访问GUI界面

打开浏览器访问：http://localhost:8081

界面包含以下标签页：
- **Inbound**：配置输入插件
- **Rule**：配置规则 (待实现)
- **Outbound**：配置输出插件 (待实现)
- **监控**：数据监控界面
- **运行时**：服务管理界面

### 3. 监控功能使用

#### 插件状态监控
1. 点击"监控"标签页
2. 选择"插件状态"子标签
3. 查看所有插件的运行状态
4. 点击"刷新状态"获取最新信息

#### 数据流监控
1. 点击"监控"标签页
2. 选择"数据监控"子标签
3. 实时查看数据流
4. 使用过滤器按插件ID或数据类型过滤
5. 开启"自动刷新"实现实时更新

#### 运行时管理
1. 点击"运行时"标签页
2. 查看服务状态
3. 使用"启动"/"停止"/"重启"按钮管理服务

## 开发指南

### 1. 在插件中添加监控

#### 状态上报
```python
# 在插件中上报状态
await self.report_status("running", {
    "memory_usage": "50MB",
    "cpu_usage": "10%",
    "last_activity": time.strftime("%H:%M:%S"),
})
```

#### 数据上报
```python
# 在数据处理时上报
await self.report_data("input", data, {
    "queue_size": self._data_queue.qsize(),
    "processing_time": 0.05,
})
```

### 2. 自定义监控组件

#### 创建自定义数据查看器
```python
from beezer.gui.components.data_viewer import DataViewer

# 创建自定义查看器
viewer = DataViewer(
    title="自定义监控",
    on_refresh=my_refresh_function,
    on_clear=my_clear_function,
)

# 更新数据
viewer.update_data(data_entries)
```

#### 扩展监控API
```python
# 在 main.py 中添加自定义API
@app.route("/api/custom/monitor", methods=["GET"])
async def custom_monitor(request):
    # 自定义监控逻辑
    return json({"custom_data": "value"})
```

### 3. 配置监控参数

#### 数据存储限制
```python
# 在 main.py 中修改
MAX_DATA_ENTRIES_PER_PLUGIN = 1000  # 每个插件最多保存的数据条目数
```

#### 自动刷新间隔
```python
# 在监控组件中配置
AUTO_REFRESH_INTERVAL = 5  # 秒
```

## API参考

### 状态上报API

#### POST /api/plugin/status
上报插件状态

**请求体：**
```json
{
    "plugin_id": "插件ID",
    "plugin_type": "插件类型",
    "status": "状态",
    "timestamp": 1646123456.789,
    "details": {
        "memory_usage": "50MB",
        "cpu_usage": "10%"
    }
}
```

#### GET /api/plugin/status
获取所有插件状态

**查询参数：**
- `plugin_type`: 按插件类型过滤
- `status`: 按状态过滤

### 数据上报API

#### POST /api/plugin/data
上报插件数据

**请求体：**
```json
{
    "plugin_id": "插件ID",
    "plugin_type": "插件类型",
    "data_type": "数据类型",
    "data": {...},
    "timestamp": 1646123456.789,
    "metadata": {...}
}
```

#### GET /api/plugin/data
获取所有插件数据

**查询参数：**
- `plugin_id`: 按插件ID过滤
- `plugin_type`: 按插件类型过滤
- `data_type`: 按数据类型过滤
- `limit`: 限制返回条目数

## 故障排除

### 常见问题

1. **GUI无法访问**
   - 检查端口8081是否被占用
   - 确认GUI服务已启动

2. **数据不显示**
   - 检查主服务是否运行在端口8000
   - 确认插件正在上报数据

3. **状态不更新**
   - 点击刷新按钮
   - 检查网络连接

### 日志查看

```bash
# 查看主服务日志
python -m beezer.main --debug

# 查看GUI日志
python -m beezer.gui --debug
```

## 扩展计划

### 即将实现的功能
1. 数据导出功能
2. 历史数据查看
3. 告警和通知
4. 性能指标图表
5. 自定义仪表板

### 长期规划
1. 分布式监控
2. 数据持久化
3. 监控数据分析
4. 自动化运维
