# Beezer GUI 实现计划

## 项目概述

Beezer GUI 是 Beezer 项目的图形化配置和监控工具，使用 Flet 框架实现，允许用户通过直观的界面配置和管理 Beezer 的 Inbound、Rule 和 Outbound 组件，并提供实时数据监控功能。

## 技术栈

- **UI 框架**: [Flet](https://github.com/flet-dev/flet) - 基于 Flutter 的 Python Web 和桌面 UI 框架
- **依赖管理**: Poetry - 维护项目依赖和构建环境
- **异步处理**: asyncio - 与 Beezer 核心逻辑交互
- **配置管理**: 复用现有的配置文件结构和格式

## 功能规划

### 1. 基础框架设计 ✅

- 创建 GUI 包结构和入口点
- 设计基本的页面布局和导航栈
- 实现命令行启动器 `beezer-gui`

### 2. 配置管理模块

- 实现配置加载和解析功能
  - 支持从 Beezer 配置文件读取配置
  - 将配置转换为可编辑的数据结构
- 实现配置保存功能
  - 将修改后的配置保存回文件
  - 支持配置验证和错误提示

### 3. Inbound 配置界面

- 显示现有 Inbound 列表
- 支持添加、删除和编辑 Inbound
- 提供常见 Inbound 类型的表单配置界面
- 实现配置验证和错误提示

### 4. Rule 配置界面

- 显示现有 Rule 列表
- 支持添加、删除和编辑 Rule
- 提供 Rule 配置表单
- 支持验证 Rule 格式和参数

### 5. Outbound 配置界面

- 显示现有 Outbound 列表
- 支持添加、删除和编辑 Outbound
- 为不同类型的 Outbound 提供专用配置表单
- 实现新的 WebSocket Outbound 类型

### 6. Beezer 运行时管理器

- 实现后台启动 Beezer 服务的功能
- 提供服务状态监控和管理接口
- 支持服务热重载（配置更改后重启）
- 错误处理和状态反馈

### 7. 数据监控界面

- 实现 Outbound 数据实时展示
  - 表格/列表视图展示数据包
  - 支持 JSON 格式化显示
- 提供过滤和搜索功能
- 支持数据导出和清除
- WebSocket Outbound 接收数据的可视化

## 开发路线图

### 第一阶段: 基础框架 ✅

- 创建项目结构和入口点
- 实现基本的 UI 布局和导航
- 添加基础按钮和标签页占位符

### 第二阶段: 配置管理

- 实现配置加载和解析
- 创建配置编辑器组件
- 实现配置保存功能

### 第三阶段: 配置界面

- 实现 Inbound 配置界面
- 实现 Rule 配置界面
- 实现 Outbound 配置界面
- 创建新的 WebSocket Outbound 类型

### 第四阶段: 运行时管理

- 实现 Beezer 服务启动器
- 添加状态监控和管理
- 实现服务热重载功能

### 第五阶段: 数据监控

- 实现数据监控界面
- 添加过滤和搜索功能
- 实现 WebSocket Outbound 数据显示

## 文件结构

```
beezer/
├── gui/
│   ├── __init__.py
│   ├── __main__.py        # 入口点
│   ├── app.py             # 主应用和 UI 框架
│   ├── config_manager.py  # 配置管理
│   ├── bee_runtime.py     # 运行时管理
│   ├── views/
│   │   ├── __init__.py
│   │   ├── inbound_view.py    # Inbound 配置视图
│   │   ├── rule_view.py       # Rule 配置视图
│   │   ├── outbound_view.py   # Outbound 配置视图
│   │   └── monitor_view.py    # 数据监控视图
│   └── components/
│       ├── __init__.py
│       ├── config_editor.py   # 通用配置编辑器组件
│       └── data_viewer.py     # 数据查看组件
└── outbounds/
    └── websocket_outbound.py  # 新的 WebSocket Outbound 实现
```

## 当前进度

- ✅ 基础框架设计
- 🔄 配置管理模块 (部分完成)
- ⬜ Inbound 配置界面
- ⬜ Rule 配置界面
- ⬜ Outbound 配置界面
- ⬜ Beezer 运行时管理器
- ⬜ 数据监控界面

## 最新更新 (Phase 2 - 配置管理)

### 已完成:
1. **配置管理器 (`config_manager.py`)** - 完成
   - 支持加载、保存、验证 Beezer 配置文件
   - 提供 CRUD 操作接口 (增删改查 Inbound、Outbound、Rule)
   - 支持配置验证和错误处理
   - 自动创建默认配置

2. **通用配置编辑器组件 (`components/config_editor.py`)** - 完成
   - 支持多种字段类型 (text, number, dropdown, checkbox, textarea)
   - 内置验证功能
   - 可复用的配置编辑界面

3. **基础 GUI 框架** - 完成并优化
   - 简化了 Flet 组件使用，解决了兼容性问题
   - 实现了基本的标签页导航
   - 添加了状态显示和操作按钮
   - GUI 应用可以正常启动并在浏览器中访问

### 当前状态:
- GUI 应用已可以正常运行 (http://localhost:8080)
- 基础界面框架已搭建完成
- 配置管理后端逻辑已实现

### 已完成 (Phase 3 - Inbound 配置界面):
4. **Inbound 配置界面** - 完成
   - 实现了完整的 Inbound 列表渲染
   - 按类型分组显示 (Modbus, HTTP Client, Siemens 等)
   - 显示详细配置信息 (IP, Port, Interval 等)
   - 添加了统计信息和刷新功能
   - 集成了配置管理器，可以正确加载和显示配置

5. **配置加载优化** - 完成
   - 修复了配置文件路径问题
   - 增强了错误处理和日志记录
   - 添加了缺失字段的自动补全
   - 支持大型配置文件的解析 (140+ Inbound 配置)

### 当前状态:
- ✅ GUI 应用正常运行，配置成功加载
- ✅ Inbound 界面可以正确显示所有配置项
- ✅ 支持按类型分组查看 (Modbus, HTTP Client, Siemens)
- ✅ 显示详细的配置统计信息

### 已完成 (Phase 4 - Inbound 配置编辑功能):
6. **Inbound 配置编辑功能** - 完成
   - 实现了动态 Inbound 编辑器 (`DynamicInboundEditor`)
   - 支持根据类型动态显示不同的配置字段
   - 支持所有 Inbound 类型的配置编辑 (Modbus, HTTP Client, Fanuc, Siemens, Focas)
   - 实现了完整的字段验证和错误处理
   - 支持添加和编辑现有 Inbound 配置
   - 集成了配置保存和刷新功能

7. **类型特定的配置表单** - 完成
   - Modbus: 协议、连接方式、IP、端口、从站地址、采集间隔
   - Fanuc: IP、端口、超时时间、采集间隔
   - Siemens: 设备型号、IP、端口、采集间隔
   - HTTP Client: 采集间隔、请求配置说明
   - Focas: 基础配置说明

8. **配置对象创建和验证** - 完成
   - 根据类型自动创建正确的配置对象 (ModbusConfig, FanucConfig 等)
   - 支持数据验证和类型转换
   - 错误处理和用户友好的错误信息

### 已完成 (Phase 4.1 - 修复编辑功能集成):
9. **GUI应用集成修复** - 完成
   - 修复了主应用使用旧的简化视图的问题
   - 将主应用更新为使用新的 `InboundView` 类
   - 确保编辑和删除按钮正确显示
   - 集成了完整的动态编辑器功能

### 当前状态:
- ✅ GUI 应用正常运行，配置成功加载
- ✅ Inbound 界面可以正确显示所有配置项
- ✅ 支持按类型分组查看 (Modbus, HTTP Client, Siemens)
- ✅ 显示详细的配置统计信息
- ✅ **完整的 Inbound 配置编辑功能**
- ✅ **动态字段生成和类型特定的配置表单**
- ✅ **配置验证和保存功能**
- ✅ **编辑和删除按钮正确显示和工作**

### 已完成 (Phase 5 - 数据监控功能):
10. **数据监控系统** - 完成
   - 扩展了 `report_plugin_status` 函数，添加了 `report_plugin_data` 函数
   - 在插件基类中添加了 `report_data` 方法
   - 修改了 InboundPlugin 和 OutboundPlugin，自动上报数据
   - 在 main.py 中添加了数据存储和API接口

11. **数据监控API** - 完成
   - `/api/plugin/data` (POST) - 接收插件数据上报
   - `/api/plugin/data` (GET) - 获取所有插件数据
   - `/api/plugin/data/<plugin_id>` (GET) - 获取指定插件数据
   - 支持按插件ID、类型、数据类型过滤
   - 支持限制返回条目数量

12. **GUI数据监控界面** - 完成
   - 实现了 `DataViewer` 组件 - 数据查看和过滤功能
   - 实现了 `MonitorView` 视图 - 插件状态和数据监控
   - 集成了实时数据展示、过滤、搜索功能
   - 添加了状态统计卡片和标签页界面

13. **Beezer运行时管理器** - 完成
   - 实现了 `BeezerRuntime` 类 - 服务启动/停止/重启功能
   - 支持进程状态监控和错误处理
   - 集成了状态变化回调机制
   - 添加了运行时管理GUI界面

### 当前状态:
- ✅ GUI 应用正常运行，配置成功加载
- ✅ Inbound 界面可以正确显示所有配置项
- ✅ 支持按类型分组查看 (Modbus, HTTP Client, Siemens)
- ✅ 显示详细的配置统计信息
- ✅ **完整的 Inbound 配置编辑功能**
- ✅ **动态字段生成和类型特定的配置表单**
- ✅ **配置验证和保存功能**
- ✅ **编辑和删除按钮正确显示和工作**
- ✅ **数据监控系统完整实现**
- ✅ **插件状态和数据实时监控**
- ✅ **运行时管理功能**

### 下一步计划 (Phase 6):
1. 优化 HTTP Client 的复杂配置编辑 (requests 字段)
2. 添加 Modbus Points 配置编辑功能
3. 实现 Rule 配置界面
4. 实现 Outbound 配置界面
5. 添加数据导出和历史查看功能
6. 优化监控界面的自动刷新和实时更新
