#!/usr/bin/env python3
"""测试配置文件验证"""

import sys
import yaml
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from beezer.type_model import ConfigModel

    print("✅ 成功导入 ConfigModel")
except Exception as e:
    print(f"❌ 导入 ConfigModel 失败: {e}")
    import traceback

    traceback.print_exc()
    sys.exit(1)


def test_config_validation():
    """测试配置文件验证"""
    config_path = project_root / "config.yaml"

    if not config_path.exists():
        print(f"❌ 配置文件不存在: {config_path}")
        return False

    try:
        # 读取YAML文件
        with open(config_path, "r", encoding="utf-8") as f:
            config_data = yaml.safe_load(f)

        print("✅ YAML文件读取成功")
        print(f"顶级键: {list(config_data.keys())}")

        # 验证配置模型
        config = ConfigModel.model_validate(config_data)
        print("✅ 配置验证成功!")

        # 打印统计信息
        print(f"Flows: {len(config.flows)}")
        print(f"Rules: {len(config.rules)}")
        print(f"Inbounds: {len(config.inbounds)}")
        print(f"Outbounds: {len(config.outbounds)}")

        # 检查第一个flow的规则
        if config.flows:
            first_flow = config.flows[0]
            print(f"\nFirst flow name: {first_flow.name}")
            print(f"First flow rules: {len(first_flow.rules)}")
            for rule in first_flow.rules:
                print(f"  Rule: {rule.name}")
                print(f"    action: {rule.action}")
                print(f"    trigger: {rule.trigger}")
                print(f"    merge_strategy: {rule.merge_strategy}")

        return True

    except Exception as e:
        print(f"❌ 配置验证失败: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_config_validation()
    sys.exit(0 if success else 1)
