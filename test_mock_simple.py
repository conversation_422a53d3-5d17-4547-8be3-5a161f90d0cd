#!/usr/bin/env python3
"""简单测试Mock插件和数据监控功能。"""

import asyncio
import time
from loguru import logger

async def test_mock_plugin():
    """测试Mock插件。"""
    logger.info("测试Mock插件...")
    
    try:
        from beezer.plugins.inbounds.mock_inbound import MockInbound
        from beezer.type_model import MockConfig
        
        # 创建配置
        config_data = {
            'type': 'mock',
            'id': 'test_mock',
            'interval': 1,
            'data_type': 'sensor',
            'error_rate': 0.1
        }
        
        # 验证配置
        mock_config = MockConfig.model_validate(config_data)
        logger.info(f"配置验证成功: {mock_config}")
        
        # 创建插件
        mock_plugin = MockInbound(config_data)
        logger.info(f"插件创建成功: {mock_plugin.config}")
        
        # 测试数据生成
        data = mock_plugin._generate_mock_data()
        logger.info(f"生成的数据: {data}")
        
        # 测试状态上报
        await mock_plugin.report_status("running", {"test": True})
        logger.info("状态上报测试完成")
        
        # 测试数据上报
        await mock_plugin.report_data("input", data, {"test": True})
        logger.info("数据上报测试完成")
        
        return True
        
    except Exception as e:
        logger.error(f"Mock插件测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_print_plugin():
    """测试Print插件。"""
    logger.info("测试Print插件...")
    
    try:
        from beezer.plugins.outbounds.print_outbound import PrintOutbound
        
        # 创建配置
        config_data = {
            'type': 'print',
            'id': 'test_print',
            'format': 'json',
            'prefix': '[TEST]',
            'max_length': 200
        }
        
        # 创建插件
        print_plugin = PrintOutbound(config_data)
        logger.info(f"Print插件创建成功: {print_plugin.config}")
        
        # 测试数据输出
        test_data = {
            'timestamp': time.time(),
            'temperature': 25.5,
            'humidity': 60.0,
            'message': 'Test data from mock plugin'
        }
        
        result = await print_plugin.action_print(test_data, {})
        logger.info(f"Print输出结果: {result}")
        
        result = await print_plugin.action_log(test_data, {})
        logger.info(f"Log输出结果: {result}")
        
        result = await print_plugin.action_debug(test_data, {})
        logger.info(f"Debug输出结果: {result}")
        
        return True
        
    except Exception as e:
        logger.error(f"Print插件测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_data_monitoring():
    """测试数据监控功能。"""
    logger.info("测试数据监控功能...")
    
    try:
        import aiohttp
        
        # 测试状态上报API
        status_data = {
            "plugin_id": "test_mock",
            "plugin_type": "MockInbound",
            "status": "running",
            "timestamp": time.time(),
            "details": {"test": True}
        }
        
        async with aiohttp.ClientSession() as session:
            try:
                async with session.post("http://localhost:8000/api/plugin/status", json=status_data) as response:
                    if response.status == 200:
                        result = await response.json()
                        logger.info(f"状态上报成功: {result}")
                    else:
                        logger.warning(f"状态上报失败: {response.status}")
            except Exception as e:
                logger.warning(f"无法连接到主服务: {e}")
        
        # 测试数据上报API
        data_entry = {
            "plugin_id": "test_mock",
            "plugin_type": "inbound",
            "data_type": "input",
            "data": {"temperature": 25.5, "humidity": 60.0},
            "timestamp": time.time(),
            "metadata": {"test": True}
        }
        
        async with aiohttp.ClientSession() as session:
            try:
                async with session.post("http://localhost:8000/api/plugin/data", json=data_entry) as response:
                    if response.status == 200:
                        result = await response.json()
                        logger.info(f"数据上报成功: {result}")
                    else:
                        logger.warning(f"数据上报失败: {response.status}")
            except Exception as e:
                logger.warning(f"无法连接到主服务: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"数据监控测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数。"""
    logger.info("=== Mock插件和数据监控测试 ===")
    
    # 测试Mock插件
    if await test_mock_plugin():
        logger.info("✅ Mock插件测试通过")
    else:
        logger.error("❌ Mock插件测试失败")
        return
    
    # 测试Print插件
    if await test_print_plugin():
        logger.info("✅ Print插件测试通过")
    else:
        logger.error("❌ Print插件测试失败")
        return
    
    # 测试数据监控
    if await test_data_monitoring():
        logger.info("✅ 数据监控测试通过")
    else:
        logger.warning("⚠️ 数据监控测试失败（可能主服务未启动）")
    
    logger.info("🎉 所有测试完成！")

if __name__ == "__main__":
    asyncio.run(main())
