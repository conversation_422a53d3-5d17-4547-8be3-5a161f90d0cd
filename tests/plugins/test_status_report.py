import pytest
from unittest.mock import patch, MagicMock, AsyncMock

# 导入测试所需的模块
from beezer.plugins.utils import report_plugin_status

# 创建模拟的plugin_status字典，避免直接导入main模块
plugin_status = {}


# 模拟的PluginType枚举
class PluginType:
    INBOUND = "inbound"
    OUTBOUND = "outbound"
    RULE = "rule"
    FLOW = "flow"


# 测试用例类，用于模拟配置
class MockConfig:
    def __init__(self, id):
        self.id = id


# 模拟的Plugin基类
class MockPlugin:
    async def report_status(self, status, details=None):
        if not hasattr(self, "config") or not hasattr(self.config, "id"):
            return False

        plugin_id = self.config.id
        plugin_type = self.__class__.__name__

        return await report_plugin_status(
            plugin_id=plugin_id, plugin_type=plugin_type, status=status, details=details
        )


# 测试Plugin基类的派生类
class TestPluginImpl(MockPlugin):
    plugin_type = PluginType.INBOUND
    plugin_protocol = "test_protocol"

    def __init__(self, config=None):
        self.config = config or MockConfig("test_plugin_1")

    async def execute(self, data=None, config=None):
        # 避免未使用参数警告
        _ = data, config
        return {"result": "success"}


# 测试report_plugin_status工具函数
@pytest.mark.asyncio
async def test_report_plugin_status():
    """测试report_plugin_status工具函数发送插件状态的功能"""
    # 模拟客户端会话和响应
    mock_response = MagicMock()
    mock_response.status = 200
    mock_response.json = AsyncMock(return_value={"success": True})

    mock_session = MagicMock()
    mock_session.__aenter__ = AsyncMock(return_value=mock_session)
    mock_session.__aexit__ = AsyncMock(return_value=None)
    mock_session.post = AsyncMock(return_value=mock_response)

    # 模拟ClientSession
    with patch("aiohttp.ClientSession", return_value=mock_session):
        # 调用report_plugin_status函数
        result = await report_plugin_status(
            plugin_id="test_plugin",
            plugin_type="TestType",
            status="running",
            details={"test": "data"},
        )

        # 验证结果
        assert result == True

        # 验证调用的URL使用了正确的端口（从配置读取）
        call_args = mock_session.post.call_args
        called_url = call_args[0][0]
        # URL应该使用配置的端口而不是硬编码的8000
        assert called_url.startswith("http://localhost:")
        assert called_url.endswith("/api/plugin/status")

        # 验证调用的数据
        called_data = call_args[1]["json"]
        assert called_data["plugin_id"] == "test_plugin"
        assert called_data["plugin_type"] == "TestType"
        assert called_data["status"] == "running"
        assert called_data["details"] == {"test": "data"}
        assert "timestamp" in called_data


# 测试Plugin基类的report_status方法
@pytest.mark.asyncio
async def test_plugin_report_status():
    """测试Plugin基类的report_status方法"""
    # 创建测试插件实例
    test_plugin = TestPluginImpl()

    # Mock report_plugin_status函数
    with patch("tests.plugins.test_status_report.report_plugin_status") as mock_report:
        mock_report.return_value = True

        # 调用report_status方法
        result = await test_plugin.report_status(
            status="idle", details={"memory_usage": "50MB"}
        )

        # 验证结果
        assert result == True

        # 验证调用参数
        mock_report.assert_called_once_with(
            plugin_id="test_plugin_1",
            plugin_type="TestPluginImpl",
            status="idle",
            details={"memory_usage": "50MB"},
        )


# 测试没有配置ID的情况
@pytest.mark.asyncio
async def test_plugin_report_status_no_config():
    """测试没有配置ID的情况"""
    # 创建没有配置的测试插件
    test_plugin = TestPluginImpl(None)
    test_plugin.config = None

    # 调用report_status方法
    result = await test_plugin.report_status("error")

    # 验证结果（应该返回False）
    assert result == False
