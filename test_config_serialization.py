#!/usr/bin/env python3
"""测试配置序列化功能。"""

import asyncio
import tempfile
import yaml
from pathlib import Path
from beezer.gui.config_manager import ConfigManager
from beezer.type_model import SiemensConfig, HttpClientConfig, InboundTypes


async def test_config_serialization():
    """测试配置序列化是否正确。"""
    print("测试配置序列化...")
    
    # 创建临时配置文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
        temp_config_path = Path(f.name)
    
    try:
        # 创建配置管理器
        config_manager = ConfigManager(temp_config_path)
        
        # 创建默认配置
        await config_manager.create_default_config()
        
        # 添加 Siemens 配置
        siemens_config = SiemensConfig(
            type=InboundTypes.Siemens,
            id="test_siemens",
            model="828D",
            ip="*************",
            port=102,
            interval=5,
            param_config={"test": "value"}
        )
        
        # 添加 HTTP Client 配置
        http_config = HttpClientConfig(
            type=InboundTypes.HttpClient,
            id="test_http",
            interval=10,
            requests=[
                {
                    "method": "GET",
                    "name": "test_request",
                    "url": "http://example.com/api",
                    "headers": {},
                    "params": {},
                    "data": None,
                    "content_type": None
                }
            ]
        )
        
        # 添加配置
        config_manager.add_inbound("siemens_1", siemens_config)
        config_manager.add_inbound("http_1", http_config)
        
        # 保存配置
        success = await config_manager.save_config()
        print(f"✅ 配置保存成功: {success}")
        
        # 读取保存的文件内容
        with open(temp_config_path, 'r', encoding='utf-8') as f:
            saved_content = f.read()
        
        print("\n=== 保存的配置内容 ===")
        print(saved_content)
        
        # 解析 YAML 检查格式
        parsed_config = yaml.safe_load(saved_content)
        
        # 检查 inbounds 部分
        inbounds = parsed_config.get('inbounds', {})
        
        print("\n=== 验证结果 ===")
        
        # 检查 Siemens 配置
        if 'siemens_1' in inbounds:
            siemens_data = inbounds['siemens_1']
            print(f"✅ Siemens type: {siemens_data.get('type')} (应该是字符串)")
            print(f"✅ Siemens ip: {siemens_data.get('ip')} (应该是字符串)")
            print(f"✅ Siemens model: {siemens_data.get('model')}")
            
            # 验证类型
            if isinstance(siemens_data.get('type'), str):
                print("✅ type 字段正确序列化为字符串")
            else:
                print("❌ type 字段未正确序列化")
                
            if isinstance(siemens_data.get('ip'), str):
                print("✅ ip 字段正确序列化为字符串")
            else:
                print("❌ ip 字段未正确序列化")
        
        # 检查 HTTP Client 配置
        if 'http_1' in inbounds:
            http_data = inbounds['http_1']
            print(f"✅ HTTP Client type: {http_data.get('type')} (应该是字符串)")
            print(f"✅ HTTP Client requests: {len(http_data.get('requests', []))} 个请求")
            
            if isinstance(http_data.get('type'), str):
                print("✅ HTTP Client type 字段正确序列化为字符串")
            else:
                print("❌ HTTP Client type 字段未正确序列化")
        
        print("\n✅ 测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理临时文件
        if temp_config_path.exists():
            temp_config_path.unlink()


if __name__ == "__main__":
    asyncio.run(test_config_serialization())
