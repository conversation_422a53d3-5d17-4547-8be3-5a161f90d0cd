apps: []
inbounds: {}
outbounds:
  custom_ioteq:
    type: custom_ioteq
    config:
      api_host: http://*************:9890/eq/eqEquipmentAccTabs/
      odbc_url: mssql+pyodbc://sa:123456@*************:1433/test?driver=ODBC+Driver+17+for+SQL+Server
rules:
- name: syntec_status
  rules:
  - source_name: current
    source:
      source: 2 if var1 else 0
      placeholder:
        var1: $.data
      mapping: {}
      default: None
    target_name: None
    target: $.State
    type: expr
  - source_name: current
    source:
      source: 2 if var1 != 1024 else 0
      placeholder:
        var1: $.data.result
      mapping: {}
      default: None
    target_name: None
    target: $.State
    type: expr
  - source_name: current
    source:
      source: $.data.path[0].status
      placeholder: {}
      mapping:
        ACTIVE: '1'
      default: '2'
    target_name: None
    target: $.State
    type: map
  - source_name: None
    source: $.id
    target_name: None
    target: $.id
    type: reference
  - source_name: None
    source:
      source: TIMESTAMP_TO_STR(var1)
      placeholder:
        var1: $.timestamp
      mapping: {}
      default: None
    target_name: None
    target: $.CreateDate
    type: expr
  - source_name: None
    source:
      source: UUID1()
      placeholder: {}
      mapping: {}
      default: None
    target_name: None
    target: $.Uuid
    type: expr
  trigger:
    mode: change
    type: any
    exclude:
    - $.CreateDate
    - $.Uuid
- name: syntec_count
  rules:
  - source_name: current
    source: $.data.part_count
    target_name: None
    target: $.Count
    type: reference
  - source_name: None
    source: $.id
    target_name: None
    target: $.id
    type: reference
  - source_name: None
    source:
      source: TIMESTAMP_TO_STR(var1)
      placeholder:
        var1: $.timestamp
      mapping: {}
      default: None
    target_name: None
    target: $.CreateDate
    type: expr
  - source_name: None
    source:
      source: UUID1()
      placeholder: {}
      mapping: {}
      default: None
    target_name: None
    target: $.Uuid
    type: expr
  trigger:
    mode: change
    type: any
    exclude:
    - $.CreateDate
    - $.Uuid
- name: syntec_alarm
  rules:
  - source_name: current
    source:
      source: 1 if (var1 and len(var1)) or var2 == 'TRIGGERED' else 0
      placeholder:
        var1: $.data.alarm
        var2: $.data.emg
      mapping: {}
      default: None
    target_name: None
    target: $.State
    type: expr
  - source_name: None
    source: $.id
    target_name: None
    target: $.id
    type: reference
  - source_name: None
    source:
      source: TIMESTAMP_TO_STR(var1)
      placeholder:
        var1: $.timestamp
      mapping: {}
      default: None
    target_name: None
    target: $.CreateDate
    type: expr
  - source_name: None
    source:
      source: UUID1()
      placeholder: {}
      mapping: {}
      default: None
    target_name: None
    target: $.Uuid
    type: expr
  trigger:
    mode: change
    type: any
    exclude:
    - $.CreateDate
    - $.Uuid
- name: syntec_current
  rules:
  - source_name: current
    source: $.data.path[0].main_prog
    target_name: None
    target: $.main_prog
    type: reference
  - source_name: current
    source: $.data.path[0].cur_prog
    target_name: None
    target: $.cur_prog
    type: reference
  - source_name: current
    source: $.data.path[0].cur_seq
    target_name: None
    target: $.cur_seq
    type: reference
  - source_name: current
    source: $.data.path[0].ov_feed
    target_name: None
    target: $.ov_feed
    type: reference
  - source_name: current
    source: $.data.path[0].act_feed
    target_name: None
    target: $.act_feed
    type: reference
  - source_name: current
    source: $.data.path[0].spindles
    target_name: None
    target: $.spindles
    type: reference
  - source_name: current
    source: $.data.path[0].axes
    target_name: None
    target: $.axes
    type: reference
  - source_name: current
    source: $.data.emg
    target_name: None
    target: $.emg
    type: reference
  - source_name: current
    source: $.data.mode
    target_name: None
    target: $.mode
    type: reference
  - source_name: current
    source: $.data.alarm
    target_name: None
    target: $.alarm
    type: reference
  - source_name: None
    source: $.id
    target_name: None
    target: $.id
    type: reference
  - source_name: None
    source:
      source: TIMESTAMP_TO_STR(var1)
      placeholder:
        var1: $.timestamp
      mapping: {}
      default: None
    target_name: None
    target: $.CreateDate
    type: expr
  - source_name: None
    source:
      source: UUID1()
      placeholder: {}
      mapping: {}
      default: None
    target_name: None
    target: $.Uuid
    type: expr
  trigger:
    mode: change
    type: any
    exclude:
    - $.CreateDate
    - $.Uuid
flows:
- name: flow01
  inbounds:
  - '1'
  outbound: custom_ioteq
  rules:
  - name: syntec_status
    trigger: None
    action: IOTEqStatues
    merge_strategy: None
  - name: syntec_alarm
    trigger: None
    action: IOTEqWarning
    merge_strategy: None
  - name: syntec_count
    trigger: None
    action: IOTEqProduceCount
    merge_strategy: None
  - name: syntec_current
    trigger: None
    action: IOTEqMachiningParams
    merge_strategy: None
server_port: '9999'
version: '1'
