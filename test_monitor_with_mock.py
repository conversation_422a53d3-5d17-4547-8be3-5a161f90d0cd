#!/usr/bin/env python3
"""使用Mock插件测试数据监控功能的脚本。

这个脚本会启动Beezer服务和GUI，使用Mock Inbound和Print Outbound进行测试。
"""

import asyncio
import subprocess
import time
import signal
import sys
import os
from loguru import logger


class MockMonitorTest:
    """Mock数据监控测试管理器。"""
    
    def __init__(self):
        self.main_process = None
        self.gui_process = None
        self.running = False
        self.config_file = "config_monitor_test.yaml"
    
    async def start_main_service(self):
        """启动Beezer主服务。"""
        logger.info("启动Beezer主服务（使用Mock配置）...")
        try:
            # 检查配置文件是否存在
            if not os.path.exists(self.config_file):
                logger.error(f"配置文件不存在: {self.config_file}")
                return False
            
            self.main_process = subprocess.Popen(
                ["python", "-m", "beezer.main", "--config", self.config_file],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
            )
            
            # 等待服务启动
            await asyncio.sleep(3)
            
            if self.main_process.poll() is None:
                logger.info(f"Beezer主服务启动成功, PID: {self.main_process.pid}")
                logger.info("服务API地址: http://localhost:8000")
                return True
            else:
                stdout, stderr = self.main_process.communicate()
                logger.error(f"Beezer主服务启动失败:")
                logger.error(f"STDOUT: {stdout}")
                logger.error(f"STDERR: {stderr}")
                return False
        except Exception as e:
            logger.error(f"启动Beezer主服务时出错: {e}")
            return False
    
    async def start_gui_service(self):
        """启动GUI服务。"""
        logger.info("启动GUI服务...")
        try:
            self.gui_process = subprocess.Popen(
                ["python", "-m", "beezer.gui"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
            )
            
            # 等待GUI启动
            await asyncio.sleep(3)
            
            if self.gui_process.poll() is None:
                logger.info(f"GUI服务启动成功, PID: {self.gui_process.pid}")
                logger.info("GUI访问地址: http://localhost:8081")
                return True
            else:
                stdout, stderr = self.gui_process.communicate()
                logger.error(f"GUI服务启动失败:")
                logger.error(f"STDOUT: {stdout}")
                logger.error(f"STDERR: {stderr}")
                return False
        except Exception as e:
            logger.error(f"启动GUI服务时出错: {e}")
            return False
    
    async def check_services(self):
        """检查服务状态。"""
        import aiohttp
        
        # 检查主服务
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get("http://localhost:8000/api/plugin/status") as response:
                    if response.status == 200:
                        data = await response.json()
                        plugin_count = data.get("count", 0)
                        logger.info(f"主服务正常，当前有 {plugin_count} 个插件")
                    else:
                        logger.warning(f"主服务响应异常: {response.status}")
        except Exception as e:
            logger.error(f"检查主服务时出错: {e}")
        
        # 检查数据
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get("http://localhost:8000/api/plugin/data?limit=5") as response:
                    if response.status == 200:
                        data = await response.json()
                        data_count = data.get("count", 0)
                        logger.info(f"数据监控正常，当前有 {data_count} 条数据")
                        
                        # 显示一些示例数据
                        plugins = data.get("plugins", {})
                        for plugin_id, entries in list(plugins.items())[:2]:
                            logger.info(f"插件 {plugin_id}: {len(entries)} 条数据")
                            if entries:
                                latest = entries[-1]
                                logger.info(f"  最新数据: {latest.get('data_type')} @ {time.strftime('%H:%M:%S', time.localtime(latest.get('timestamp', 0)))}")
                    else:
                        logger.warning(f"数据API响应异常: {response.status}")
        except Exception as e:
            logger.error(f"检查数据API时出错: {e}")
    
    async def start_all(self):
        """启动所有服务。"""
        logger.info("=== 启动Mock数据监控测试 ===")
        
        # 启动主服务
        if not await self.start_main_service():
            logger.error("主服务启动失败，退出")
            return False
        
        # 启动GUI服务
        if not await self.start_gui_service():
            logger.error("GUI服务启动失败，退出")
            await self.stop_all()
            return False
        
        # 等待服务稳定
        await asyncio.sleep(5)
        
        # 检查服务状态
        await self.check_services()
        
        self.running = True
        logger.info("=== 所有服务启动完成 ===")
        logger.info("Mock插件正在生成测试数据...")
        logger.info("请访问 http://localhost:8081 查看GUI界面")
        logger.info("在GUI中点击'监控'标签页查看实时数据")
        logger.info("按 Ctrl+C 停止所有服务")
        
        return True
    
    async def stop_all(self):
        """停止所有服务。"""
        logger.info("=== 停止所有服务 ===")
        self.running = False
        
        # 停止GUI进程
        if self.gui_process and self.gui_process.poll() is None:
            logger.info("停止GUI服务...")
            self.gui_process.terminate()
            try:
                self.gui_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.gui_process.kill()
        
        # 停止主进程
        if self.main_process and self.main_process.poll() is None:
            logger.info("停止Beezer主服务...")
            self.main_process.terminate()
            try:
                self.main_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.main_process.kill()
        
        logger.info("=== 所有服务已停止 ===")
    
    async def run(self):
        """运行测试。"""
        # 设置信号处理
        def signal_handler(signum, frame):
            logger.info("收到停止信号，正在关闭...")
            asyncio.create_task(self.stop_all())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # 启动所有服务
        if not await self.start_all():
            return
        
        # 保持运行并定期检查
        try:
            check_interval = 30  # 30秒检查一次
            last_check = time.time()
            
            while self.running:
                await asyncio.sleep(1)
                
                # 检查进程状态
                if self.main_process and self.main_process.poll() is not None:
                    logger.error("Beezer主服务意外退出")
                    break
                
                if self.gui_process and self.gui_process.poll() is not None:
                    logger.error("GUI服务意外退出")
                    break
                
                # 定期检查服务状态
                if time.time() - last_check > check_interval:
                    await self.check_services()
                    last_check = time.time()
        
        except KeyboardInterrupt:
            logger.info("收到键盘中断信号")
        
        finally:
            await self.stop_all()


async def main():
    """主函数。"""
    test = MockMonitorTest()
    await test.run()


if __name__ == "__main__":
    print("=== Beezer Mock数据监控测试 ===")
    print("这个测试将启动:")
    print("1. Beezer 主服务 (http://localhost:8000) - 使用Mock插件")
    print("2. GUI 界面 (http://localhost:8081)")
    print("3. Mock Inbound插件 - 生成模拟数据")
    print("4. Print Outbound插件 - 打印数据到控制台")
    print()
    print("Mock插件会生成以下类型的数据:")
    print("- 传感器数据 (温度、湿度、压力、振动)")
    print("- 机器数据 (速度、负载、功率、生产数据)")
    print("- Modbus数据 (寄存器、线圈)")
    print("- 通用数据 (数值、标志、消息)")
    print()
    print("请确保端口 8000 和 8081 没有被占用")
    print("按 Enter 继续，或 Ctrl+C 取消...")
    
    try:
        input()
    except KeyboardInterrupt:
        print("\n已取消")
        sys.exit(0)
    
    asyncio.run(main())
