#!/usr/bin/env python3
"""测试配置文件加载的脚本。"""

import sys
import traceback
from loguru import logger

def test_config_loading():
    """测试配置文件加载。"""
    try:
        logger.info("开始测试配置文件加载...")
        
        # 测试导入
        logger.info("测试导入模块...")
        from beezer.config import YamlConfig
        from beezer.type_model import MockConfig
        from beezer.plugins.inbounds.mock_inbound import MockInbound
        from beezer.plugins.outbounds.print_outbound import PrintOutbound
        logger.info("模块导入成功")
        
        # 测试配置加载
        logger.info("测试配置文件加载...")
        config = YamlConfig('config_monitor_test.yaml')
        logger.info("配置文件加载成功")
        
        # 显示配置信息
        logger.info(f"Inbounds: {list(config.inbounds.keys())}")
        logger.info(f"Outbounds: {list(config.outbounds.keys())}")
        logger.info(f"Rules: {len(config.rules)}")
        logger.info(f"Flows: {len(config.flows)}")
        
        # 测试Mock配置验证
        logger.info("测试Mock配置验证...")
        for inbound_id, inbound_config in config.inbounds.items():
            if hasattr(inbound_config, 'type') and inbound_config.type == 'mock':
                logger.info(f"Mock Inbound {inbound_id}: {inbound_config}")
                
                # 创建Mock插件实例
                mock_plugin = MockInbound(inbound_config.model_dump())
                logger.info(f"Mock插件创建成功: {mock_plugin.config}")
        
        logger.info("所有测试通过！")
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        traceback.print_exc()
        return False

def test_simple_mock():
    """测试简单的Mock配置。"""
    try:
        logger.info("测试简单Mock配置...")
        
        from beezer.type_model import MockConfig
        from beezer.plugins.inbounds.mock_inbound import MockInbound
        
        # 创建简单配置
        config_data = {
            'type': 'mock',
            'id': 'test_mock',
            'interval': 3,
            'data_type': 'sensor',
            'error_rate': 0.05
        }
        
        # 验证配置
        mock_config = MockConfig.model_validate(config_data)
        logger.info(f"MockConfig验证成功: {mock_config}")
        
        # 创建插件
        mock_plugin = MockInbound(config_data)
        logger.info(f"MockInbound创建成功: {mock_plugin.config}")
        
        return True
        
    except Exception as e:
        logger.error(f"简单Mock测试失败: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    logger.info("=== Beezer 配置测试 ===")
    
    # 测试简单Mock
    if test_simple_mock():
        logger.info("✅ 简单Mock测试通过")
    else:
        logger.error("❌ 简单Mock测试失败")
        sys.exit(1)
    
    # 测试配置文件加载
    if test_config_loading():
        logger.info("✅ 配置文件测试通过")
    else:
        logger.error("❌ 配置文件测试失败")
        sys.exit(1)
    
    logger.info("🎉 所有测试通过！")
