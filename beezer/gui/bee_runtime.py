"""Beezer 运行时管理器。

提供启动、停止、重启 Beezer 服务的功能。
"""

import asyncio
import subprocess
import signal
import os
import time
from typing import Optional, Dict, Any, Callable
from loguru import logger
from pathlib import Path


class BeezerRuntime:
    """Beezer 运行时管理器。"""

    def __init__(self, config_path: Optional[str] = None):
        """初始化运行时管理器。

        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path or "config.yaml"
        self.process: Optional[subprocess.Popen] = None
        self.status = "stopped"  # stopped, starting, running, stopping, error
        self.start_time: Optional[float] = None
        self.error_message: Optional[str] = None
        
        # 状态变化回调
        self.status_callbacks: list[Callable[[str, Dict[str, Any]], None]] = []

    def add_status_callback(self, callback: Callable[[str, Dict[str, Any]], None]):
        """添加状态变化回调函数。

        Args:
            callback: 回调函数，接收状态和详细信息
        """
        self.status_callbacks.append(callback)

    def _notify_status_change(self, status: str, details: Optional[Dict[str, Any]] = None):
        """通知状态变化。

        Args:
            status: 新状态
            details: 详细信息
        """
        self.status = status
        details = details or {}
        details.update({
            "timestamp": time.time(),
            "config_path": self.config_path,
            "pid": self.process.pid if self.process else None,
            "start_time": self.start_time,
            "error_message": self.error_message,
        })
        
        logger.info(f"Beezer 状态变化: {status}")
        
        for callback in self.status_callbacks:
            try:
                callback(status, details)
            except Exception as e:
                logger.error(f"状态回调执行失败: {e}")

    async def start(self) -> bool:
        """启动 Beezer 服务。

        Returns:
            是否启动成功
        """
        if self.status in ["starting", "running"]:
            logger.warning("Beezer 服务已在运行或正在启动")
            return False

        try:
            self._notify_status_change("starting")
            
            # 检查配置文件是否存在
            if not Path(self.config_path).exists():
                self.error_message = f"配置文件不存在: {self.config_path}"
                self._notify_status_change("error")
                return False

            # 构建启动命令
            cmd = ["python", "-m", "beezer.main", "--config", self.config_path]
            
            # 启动进程
            self.process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True,
            )
            
            self.start_time = time.time()
            self.error_message = None
            
            # 等待一段时间检查进程是否正常启动
            await asyncio.sleep(2)
            
            if self.process.poll() is None:
                # 进程仍在运行
                self._notify_status_change("running")
                
                # 启动监控任务
                asyncio.create_task(self._monitor_process())
                
                logger.info(f"Beezer 服务启动成功, PID: {self.process.pid}")
                return True
            else:
                # 进程已退出
                stdout, stderr = self.process.communicate()
                self.error_message = f"启动失败: {stderr}"
                self._notify_status_change("error")
                logger.error(f"Beezer 服务启动失败: {stderr}")
                return False

        except Exception as e:
            self.error_message = f"启动异常: {str(e)}"
            self._notify_status_change("error")
            logger.error(f"启动 Beezer 服务时出错: {e}")
            return False

    async def stop(self) -> bool:
        """停止 Beezer 服务。

        Returns:
            是否停止成功
        """
        if self.status in ["stopped", "stopping"]:
            logger.warning("Beezer 服务已停止或正在停止")
            return False

        if not self.process:
            self._notify_status_change("stopped")
            return True

        try:
            self._notify_status_change("stopping")
            
            # 发送终止信号
            if os.name == 'nt':  # Windows
                self.process.terminate()
            else:  # Unix/Linux
                self.process.send_signal(signal.SIGTERM)
            
            # 等待进程退出
            try:
                await asyncio.wait_for(
                    asyncio.create_task(self._wait_for_process_exit()),
                    timeout=10.0
                )
            except asyncio.TimeoutError:
                # 强制杀死进程
                logger.warning("进程未在超时时间内退出，强制杀死")
                if os.name == 'nt':
                    self.process.kill()
                else:
                    self.process.send_signal(signal.SIGKILL)
                await asyncio.create_task(self._wait_for_process_exit())

            self.process = None
            self.start_time = None
            self.error_message = None
            self._notify_status_change("stopped")
            
            logger.info("Beezer 服务已停止")
            return True

        except Exception as e:
            self.error_message = f"停止异常: {str(e)}"
            self._notify_status_change("error")
            logger.error(f"停止 Beezer 服务时出错: {e}")
            return False

    async def restart(self) -> bool:
        """重启 Beezer 服务。

        Returns:
            是否重启成功
        """
        logger.info("重启 Beezer 服务")
        
        # 先停止
        if self.status != "stopped":
            if not await self.stop():
                return False
        
        # 等待一段时间
        await asyncio.sleep(1)
        
        # 再启动
        return await self.start()

    async def _wait_for_process_exit(self):
        """等待进程退出。"""
        if self.process:
            while self.process.poll() is None:
                await asyncio.sleep(0.1)

    async def _monitor_process(self):
        """监控进程状态。"""
        while self.process and self.process.poll() is None:
            await asyncio.sleep(1)
        
        # 进程已退出
        if self.process:
            return_code = self.process.returncode
            if return_code != 0:
                stdout, stderr = self.process.communicate()
                self.error_message = f"进程异常退出 (代码: {return_code}): {stderr}"
                self._notify_status_change("error")
            else:
                self._notify_status_change("stopped")
            
            self.process = None
            self.start_time = None

    def get_status(self) -> Dict[str, Any]:
        """获取当前状态信息。

        Returns:
            状态信息字典
        """
        uptime = None
        if self.start_time and self.status == "running":
            uptime = time.time() - self.start_time

        return {
            "status": self.status,
            "config_path": self.config_path,
            "pid": self.process.pid if self.process else None,
            "start_time": self.start_time,
            "uptime": uptime,
            "error_message": self.error_message,
        }

    def is_running(self) -> bool:
        """检查服务是否正在运行。

        Returns:
            是否正在运行
        """
        return self.status == "running" and self.process and self.process.poll() is None

    def get_logs(self, lines: int = 50) -> str:
        """获取日志信息。

        Args:
            lines: 返回的行数

        Returns:
            日志内容
        """
        # TODO: 实现日志读取功能
        # 可以从日志文件或进程输出中读取
        return "日志功能待实现"

    def __del__(self):
        """析构函数，确保进程被正确清理。"""
        if self.process and self.process.poll() is None:
            try:
                self.process.terminate()
            except:
                pass
