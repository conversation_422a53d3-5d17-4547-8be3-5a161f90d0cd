"""简化的 Inbound 配置视图。

提供基础的 Inbound 配置管理功能。
"""

import flet as ft
from typing import Dict, Any, Optional
from loguru import logger

from beezer.gui.config_manager import ConfigManager
from beezer.type_model import InboundTypes


def create_inbound_view(config_manager: ConfigManager) -> ft.Container:
    """创建 Inbound 配置视图。

    Args:
        config_manager: 配置管理器

    Returns:
        ft.Container: Inbound 配置视图容器
    """

    # 存储当前编辑的 inbound 数据
    current_inbound_data = {}

    # 创建 Inbound 列表
    inbound_list = ft.ListView(expand=True, spacing=10, padding=20)

    # 创建编辑表单
    inbound_id_field = ft.TextField(
        label="Inbound ID",
        hint_text="输入唯一的 Inbound 标识符",
        width=300,
    )

    inbound_type_dropdown = ft.Dropdown(
        label="类型",
        hint_text="选择 Inbound 类型",
        width=300,
        options=[ft.dropdown.Option(t.value) for t in InboundTypes],
    )

    # Modbus 特定字段
    modbus_ip_field = ft.TextField(
        label="IP 地址",
        hint_text="例如: *************",
        width=300,
        visible=False,
    )

    modbus_port_field = ft.TextField(
        label="端口",
        hint_text="例如: 502",
        width=300,
        keyboard_type=ft.KeyboardType.NUMBER,
        visible=False,
    )

    modbus_slave_field = ft.TextField(
        label="从站地址",
        hint_text="例如: 1",
        width=300,
        keyboard_type=ft.KeyboardType.NUMBER,
        visible=False,
        value="1",
    )

    interval_field = ft.TextField(
        label="采集间隔 (秒)",
        hint_text="例如: 1",
        width=300,
        keyboard_type=ft.KeyboardType.NUMBER,
        value="1",
    )

    # HTTP Client 特定字段
    http_url_field = ft.TextField(
        label="URL",
        hint_text="例如: http://localhost:8080/api/data",
        width=400,
        visible=False,
    )

    http_method_dropdown = ft.Dropdown(
        label="HTTP 方法",
        width=300,
        options=[
            ft.dropdown.Option("GET"),
            ft.dropdown.Option("POST"),
            ft.dropdown.Option("PUT"),
            ft.dropdown.Option("DELETE"),
        ],
        value="GET",
        visible=False,
    )

    # Fanuc/Siemens 特定字段
    cnc_ip_field = ft.TextField(
        label="CNC IP 地址",
        hint_text="例如: *************",
        width=300,
        visible=False,
    )

    cnc_port_field = ft.TextField(
        label="CNC 端口",
        hint_text="Fanuc: 8193, Siemens: 102",
        width=300,
        keyboard_type=ft.KeyboardType.NUMBER,
        visible=False,
    )

    # 状态文本
    status_text = ft.Text("", color="red", visible=False)

    def show_type_specific_fields(inbound_type: str):
        """根据 Inbound 类型显示相应的字段。"""
        # 隐藏所有特定字段
        modbus_ip_field.visible = False
        modbus_port_field.visible = False
        modbus_slave_field.visible = False
        http_url_field.visible = False
        http_method_dropdown.visible = False
        cnc_ip_field.visible = False
        cnc_port_field.visible = False

        # 根据类型显示相应字段
        if inbound_type == InboundTypes.Modbus:
            modbus_ip_field.visible = True
            modbus_port_field.visible = True
            modbus_slave_field.visible = True
        elif inbound_type == InboundTypes.HttpClient:
            http_url_field.visible = True
            http_method_dropdown.visible = True
        elif inbound_type in [InboundTypes.Fanuc, InboundTypes.Siemens]:
            cnc_ip_field.visible = True
            cnc_port_field.visible = True
            if inbound_type == InboundTypes.Fanuc:
                cnc_port_field.value = "8193"
            elif inbound_type == InboundTypes.Siemens:
                cnc_port_field.value = "102"

    def on_type_change(e):
        """类型选择变化事件。"""
        if inbound_type_dropdown.value:
            show_type_specific_fields(inbound_type_dropdown.value)
            e.page.update()

    inbound_type_dropdown.on_change = on_type_change

    def clear_form():
        """清空表单。"""
        inbound_id_field.value = ""
        inbound_type_dropdown.value = None
        modbus_ip_field.value = ""
        modbus_port_field.value = ""
        modbus_slave_field.value = "1"
        interval_field.value = "1"
        http_url_field.value = ""
        http_method_dropdown.value = "GET"
        cnc_ip_field.value = ""
        cnc_port_field.value = ""
        status_text.visible = False
        show_type_specific_fields("")

    def validate_form() -> tuple[bool, str]:
        """验证表单数据。"""
        if not inbound_id_field.value:
            return False, "请输入 Inbound ID"

        if not inbound_type_dropdown.value:
            return False, "请选择 Inbound 类型"

        inbound_type = inbound_type_dropdown.value

        if inbound_type == InboundTypes.Modbus:
            if not modbus_ip_field.value:
                return False, "请输入 Modbus IP 地址"
            if not modbus_port_field.value:
                return False, "请输入 Modbus 端口"
        elif inbound_type == InboundTypes.HttpClient:
            if not http_url_field.value:
                return False, "请输入 HTTP URL"
        elif inbound_type in [InboundTypes.Fanuc, InboundTypes.Siemens]:
            if not cnc_ip_field.value:
                return False, "请输入 CNC IP 地址"
            if not cnc_port_field.value:
                return False, "请输入 CNC 端口"

        return True, ""

    def collect_form_data() -> Dict[str, Any]:
        """收集表单数据。"""
        data = {
            "type": inbound_type_dropdown.value,
            "id": inbound_id_field.value,
            "interval": int(interval_field.value) if interval_field.value else 1,
        }

        inbound_type = inbound_type_dropdown.value

        if inbound_type == InboundTypes.Modbus:
            data.update(
                {
                    "ip": modbus_ip_field.value,
                    "port": int(modbus_port_field.value),
                    "slave": int(modbus_slave_field.value)
                    if modbus_slave_field.value
                    else 1,
                    "points": [],  # 暂时为空，后续可以扩展
                }
            )
        elif inbound_type == InboundTypes.HttpClient:
            data.update(
                {
                    "requests": [
                        {
                            "method": http_method_dropdown.value,
                            "name": "default",
                            "url": http_url_field.value,
                            "headers": {},
                            "params": {},
                        }
                    ]
                }
            )
        elif inbound_type == InboundTypes.Fanuc:
            data.update(
                {
                    "ip": cnc_ip_field.value,
                    "port": int(cnc_port_field.value),
                    "timeout": 10,
                }
            )
        elif inbound_type == InboundTypes.Siemens:
            data.update(
                {
                    "ip": cnc_ip_field.value,
                    "port": int(cnc_port_field.value),
                    "model": "828D",
                    "param_config": {},
                }
            )

        return data

    def save_inbound(e):
        """保存 Inbound 配置。"""
        try:
            # 验证表单
            is_valid, error_msg = validate_form()
            if not is_valid:
                status_text.value = error_msg
                status_text.color = "red"
                status_text.visible = True
                e.page.update()
                return

            # 收集数据
            data = collect_form_data()

            # 这里暂时只是记录日志，实际保存功能需要完善配置模型
            logger.info(f"保存 Inbound 配置: {data}")

            # 显示成功消息
            status_text.value = f"Inbound '{data['id']}' 保存成功"
            status_text.color = "green"
            status_text.visible = True

            # 清空表单
            clear_form()

            # 刷新列表
            refresh_inbound_list()

            e.page.update()

        except Exception as ex:
            logger.error(f"保存 Inbound 失败: {ex}")
            status_text.value = f"保存失败: {str(ex)}"
            status_text.color = "red"
            status_text.visible = True
            e.page.update()

    def refresh_inbound_list():
        """刷新 Inbound 列表。"""
        inbound_list.controls.clear()

        # 获取配置
        inbounds = config_manager.get_inbounds()

        if not inbounds:
            inbound_list.controls.append(
                ft.Container(
                    content=ft.Text(
                        "暂无 Inbound 配置",
                        text_align=ft.TextAlign.CENTER,
                        size=16,
                        color="grey",
                    ),
                    alignment=ft.alignment.center,
                    height=100,
                )
            )
        else:
            for inbound_id, inbound_config in inbounds.items():
                # 创建 Inbound 卡片
                card = ft.Card(
                    content=ft.Container(
                        content=ft.Row(
                            [
                                ft.Column(
                                    [
                                        ft.Text(
                                            f"{inbound_id} ({inbound_config.type})",
                                            weight=ft.FontWeight.BOLD,
                                        ),
                                        ft.Text(
                                            f"ID: {getattr(inbound_config, 'id', inbound_id)}",
                                            size=12,
                                        ),
                                    ],
                                    expand=True,
                                ),
                                ft.IconButton(
                                    icon=ft.Icons.DELETE,
                                    tooltip="删除",
                                    icon_color="red",
                                    on_click=lambda e, id=inbound_id: delete_inbound(
                                        e, id
                                    ),
                                ),
                            ]
                        ),
                        padding=15,
                    ),
                )
                inbound_list.controls.append(card)

    def delete_inbound(e, inbound_id: str):
        """删除 Inbound。"""
        try:
            if config_manager.remove_inbound(inbound_id):
                logger.info(f"删除 Inbound: {inbound_id} (仅内存，需保存配置才生效)")
                refresh_inbound_list()
                e.page.update()
        except Exception as ex:
            logger.error(f"删除 Inbound 失败: {ex}")

    # 创建表单容器
    form_container = ft.Container(
        content=ft.Column(
            [
                ft.Text("添加 Inbound", size=20, weight=ft.FontWeight.BOLD),
                ft.Divider(),
                inbound_id_field,
                inbound_type_dropdown,
                interval_field,
                # Modbus 字段
                modbus_ip_field,
                modbus_port_field,
                modbus_slave_field,
                # HTTP Client 字段
                http_url_field,
                http_method_dropdown,
                # CNC 字段
                cnc_ip_field,
                cnc_port_field,
                status_text,
                ft.Row(
                    [
                        ft.ElevatedButton(
                            text="保存",
                            icon=ft.Icons.SAVE,
                            on_click=save_inbound,
                        ),
                        ft.OutlinedButton(
                            text="清空",
                            icon=ft.Icons.CLEAR,
                            on_click=lambda e: clear_form(),
                        ),
                    ]
                ),
            ]
        ),
        padding=20,
        width=500,
    )

    # 创建列表容器
    list_container = ft.Container(
        content=ft.Column(
            [
                ft.Row(
                    [
                        ft.Text("Inbound 列表", size=20, weight=ft.FontWeight.BOLD),
                        ft.Container(expand=True),
                        ft.IconButton(
                            icon=ft.Icons.REFRESH,
                            tooltip="刷新",
                            on_click=lambda e: refresh_inbound_list(),
                        ),
                    ]
                ),
                ft.Divider(),
                inbound_list,
            ]
        ),
        expand=True,
        padding=20,
    )

    # 初始化列表
    refresh_inbound_list()

    # 返回主容器
    return ft.Container(
        content=ft.Row(
            [
                form_container,
                ft.VerticalDivider(),
                list_container,
            ],
            expand=True,
        ),
        expand=True,
    )
