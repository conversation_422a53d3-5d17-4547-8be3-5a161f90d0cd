from typing import Dict, Any, List, Optional, Type
from beezer.plugins._base import (
    FlowPlugin,
    InboundPlugin,
    OutboundPlugin,
    RulePlugin,
    Plugin,
    PluginMeta,
    PluginType,
)
from beezer.type_model import (
    FlowModel,
    RuleModel,
    OutBoundModel,
    InboundConfig,
    ConfigModel,
    FlowRuleConfigModel,
)
from loguru import logger


class BasicFlow(FlowPlugin):
    """Basic flow implementation that connects inbound sources to outbound destinations through rules"""

    plugin_type = PluginType.FLOW

    def __init__(self, flow_config: FlowModel, app_config: ConfigModel):
        """Initialize the flow with configuration

        Args:
            flow_config: Flow configuration model containing inbound, rule, and outbound settings
            app_config: Application configuration model containing all configs including rules registry
        """
        super().__init__(flow_config, app_config)
        self.flow_config = flow_config
        self.app_config = app_config
        self._setup_done = False

    async def execute(self, data: Any = None, config: Dict[str, Any] = None) -> Any:
        """Execute flow functionality - performs one-time setup if not already done

        Args:
            data: Input data (not used in flow)
            config: Configuration (not used in flow)

        Returns:
            None as flow is event-driven
        """
        if not self._setup_done:
            await self._setup()
            self._setup_done = True
            # Start flow processing after setup
            await self.start()

        return None

    async def _setup(self):
        """Internal method to set up the flow by initializing plugins and connections"""
        # Initialize outbound if specified
        outbound_name = self.flow_config.outbound
        outbound_config = self.app_config.outbounds.get(outbound_name)
        if outbound_config:
            outbound_type = Plugin.get_plugin_by_protocol(
                PluginType.OUTBOUND, outbound_config.type
            )
            if outbound_type and issubclass(outbound_type, OutboundPlugin):
                # outbound_config 现在是强类型配置对象，直接传递其字典表示
                config_dict = (
                    outbound_config.model_dump()
                    if hasattr(outbound_config, "model_dump")
                    else outbound_config
                )
                self.outbound = outbound_type(config_dict)
                self.outbound_config = config_dict

        logger.debug(f"Initializing flow with outbound: {self.outbound}")

        # Initialize and add rules
        for flow_rule in self.flow_config.rules:
            # Check if this is an aggregation rule (has merge_strategy)
            if hasattr(flow_rule, "merge_strategy") and flow_rule.merge_strategy:
                # Create data aggregation rule
                from beezer.type_model import RuleModel, RuleTriggerModel

                # Create a dummy rule model for aggregation rule
                dummy_rule_def = RuleModel(
                    name=flow_rule.name,
                    rules=[],  # Aggregation rules don't use traditional rule items
                    trigger=RuleTriggerModel(mode="change"),
                )

                rule_type = Plugin.get_plugin_by_protocol(
                    PluginType.RULE, "data_aggregation"
                )
                if rule_type and issubclass(rule_type, RulePlugin):
                    rule = rule_type(dummy_rule_def)
                    self.add_rule(rule, flow_rule)
                    logger.debug(f"Initializing flow with aggregation rule: {rule}")
            else:
                # Find matching rule definition in app config for traditional rules
                rule_def = next(
                    (r for r in self.app_config.rules if r.name == flow_rule.name), None
                )
                if rule_def:
                    # Get rule plugin type, default to data_mapping
                    rule_type = Plugin.get_plugin_by_protocol(
                        PluginType.RULE,
                        rule_def.type if hasattr(rule_def, "type") else "data_mapping",
                    )
                    if rule_type and issubclass(rule_type, RulePlugin):
                        rule = rule_type(rule_def)  # Create rule instance
                        rule.rules = rule_def.rules  # Set rule transformation rules
                        rule.trigger = rule_def.trigger  # Set trigger configuration
                        self.add_rule(rule, flow_rule)
                        logger.debug(f"Initializing flow with rule: {rule}")

        # Initialize and add inbounds
        for inbound_id in self.flow_config.inbounds:
            inbound_config = self.app_config.inbounds.get(inbound_id)
            if inbound_config:
                inbound_type = Plugin.get_plugin_by_protocol(
                    PluginType.INBOUND, inbound_config.type
                )
                if inbound_type and issubclass(inbound_type, InboundPlugin):
                    # inbound_config 现在是强类型配置对象，需要转换为字典
                    config_dict = (
                        inbound_config.model_dump()
                        if hasattr(inbound_config, "model_dump")
                        else inbound_config
                    )
                    inbound = inbound_type(config_dict)
                    self.inbounds.append(inbound)
        logger.debug(f"Initializing flow with inbounds: {self.inbounds}")

    async def cleanup(self):
        """Clean up flow resources"""
        await self.stop()
