from typing import Dict, Any
from jsonpath_ng import parse
from beezer.plugins._base import RulePlugin
from beezer.plugins._utils import safe_eval
from beezer.type_model import (
    RuleTypes,
    RuleModel,
    FlowRuleConfigModel,
    RuleTriggerModel,
)
from loguru import logger
import traceback


class DataMappingRule(RulePlugin):
    """Rule plugin for mapping data between different structures with support for
    reference mapping, fixed values, and expression evaluation"""

    plugin_protocol = RuleTypes.DataMapping  # 使用枚举值作为协议类型

    def __init__(self, config: RuleModel):
        super().__init__(config)
        self._exclude_paths = config.trigger.exclude

    def should_trigger(
        self, data: Any, trigger_config: RuleTriggerModel, inbound_id: str = None
    ) -> bool:
        """Override should_trigger to implement path-specific change detection

        Args:
            data: Current data
            trigger_config: Trigger configuration
            inbound_id: Input source ID

        Returns:
            Whether outbound should be triggered
        """
        if trigger_config.mode == "interval":
            return True

        # Update exclude paths for next transform
        self._exclude_paths = getattr(trigger_config, "exclude", [])

        if inbound_id is None:
            return True

        state = self._get_inbound_state(inbound_id)
        return state.get("last_changed", False)

    def transform(
        self, data: Any, flow_rule_config: FlowRuleConfigModel, inbound_id: str = None
    ) -> Dict[str, Any]:
        """Transform data according to rules defined in config

        Args:
            data: Input data to transform
            flow_rule_config: FlowRuleConfigModel
            inbound_id: Input source ID

        Returns:
            Transformed data dictionary
        """
        logger.debug(data)
        out_data = {}

        state = self._get_inbound_state(inbound_id)
        state["last_changed"] = False

        # Store all transformed values first
        transformed_values = {}

        for item in self.config.rules:
            # 如果flow_rule_config.trigger存在且不为空，则检查source_name是否在trigger列表中
            if (
                flow_rule_config.trigger
                and len(flow_rule_config.trigger) > 0
                and item.source_name not in flow_rule_config.trigger
            ):
                continue

            value = None
            try:
                rule_type = item.type
                target_path = item.target

                if rule_type == "reference":
                    jsonpath_expr = parse(item.source)
                    match = jsonpath_expr.find(data)
                    if match:
                        value = match[0].value
                elif rule_type == "fixed":
                    value = item.source
                elif rule_type == "expr":
                    placeholder = {}
                    for var_name, path in item.source.placeholder.items():
                        v = parse(path).find(data)
                        placeholder[var_name] = v[0].value if v else None
                    expr = item.source.source
                    value = safe_eval(expr, locals=placeholder)
                elif rule_type == "map":
                    # 获取原始值
                    jsonpath_expr = parse(item.source.source)
                    match = jsonpath_expr.find(data)
                    if match:
                        original_value = str(match[0].value)
                        # 根据映射表转换值
                        value = item.source.mapping.get(original_value)
                        if value is None and item.source.default is not None:
                            value = item.source.default
            except Exception as e:
                logger.error(f"Error processing rule {item}, {traceback.format_exc()}")

            if value is not None:
                if target_path not in self._exclude_paths:
                    transformed_values[target_path] = value
                out_data = parse(target_path).update_or_create(out_data, value)

        # Apply all transformed values and check for changes
        if transformed_values != state["last_data"]:
            state["last_changed"] = True
            state["last_data"] = transformed_values

        # for target_path, value in transformed_values.items():
        #     # Check for changes if path is not excluded
        #         old_value = state["last_data"].get(target_path)
        #         if old_value != value:
        #             state['last_changed'] = True
        #         state['last_data'][target_path] = value
        return out_data


def main():
    data = {
        "id": "syntecv3_231",
        "timestamp": 1736231239.1262808,
        "name": "current",
        "status": 200,
        "data": {
            "path": [
                {
                    "main_prog": "002",
                    "cur_prog": "002",
                    "cur_seq": 40,
                    "cur_block": "",
                    "status": "ACTIVE",
                    "axes": [
                        {
                            "name": "X",
                            "mach": 0,
                            "abs": 0,
                            "rel": 0,
                            "dist": 0,
                            "load": 0,
                        },
                        {
                            "name": "Y",
                            "mach": 0,
                            "abs": 0,
                            "rel": 0,
                            "dist": 0,
                            "load": 0,
                        },
                        {
                            "name": "Z",
                            "mach": 0,
                            "abs": 0,
                            "rel": 0,
                            "dist": 0,
                            "load": 0,
                        },
                        {
                            "name": "X2",
                            "mach": 0,
                            "abs": 0,
                            "rel": 0,
                            "dist": 0,
                            "load": 0,
                        },
                        {
                            "name": "Z2",
                            "mach": 0,
                            "abs": 0,
                            "rel": 0,
                            "dist": 0,
                            "load": 0,
                        },
                        {
                            "name": "C",
                            "mach": 0,
                            "abs": 0,
                            "rel": 0,
                            "dist": 0,
                            "load": 0,
                        },
                        {
                            "name": "C2",
                            "mach": 0,
                            "abs": 0,
                            "rel": 0,
                            "dist": 0,
                            "load": 0,
                        },
                    ],
                    "act_feed": 0,
                    "ov_feed": 0,
                    "spindles": [{"name": "S", "speed": 0, "load": 0, "ov_spindle": 0}],
                }
            ],
            "emg": "ARMED",
            "mode": "AUTOMATIC",
            "part_count": 222,
            "alarm": [],
            "result": 0,
        },
        "error": None,
    }
    current_config = {
        "name": "syntec_current",
        "description": "current",
        "trigger": {
            "mode": "change",
            "type": "any",
            "exclude": ["$.main_prog", "$.cur_prog", "$.cur_seq", "$.uuid"],
        },
        "rules": [
            {
                "source": "$.data.path[0].main_prog",
                "source_name": "current",
                "target": "$.main_prog",
                "type": "reference",
            },
            {
                "source": "$.data.path[0].cur_prog",
                "source_name": "current",
                "target": "$.cur_prog",
                "type": "reference",
            },
            {
                "source": "$.data.path[0].cur_seq",
                "source_name": "current",
                "target": "$.cur_seq",
                "type": "reference",
            },
            {
                "source": "$.data.path[0].cur_block",
                "source_name": "current",
                "target": "$.cur_block",
                "type": "reference",
            },
            {
                "source": {
                    "source": "UUID1()",
                },
                "source_name": "current",
                "target": "$.uuid",
                "type": "expr",
            },
            {
                "source": {
                    "source": "2 if var1 else 0",
                    "placeholder": {"var1": "$.status"},
                },
                "source_name": "current",
                "target": "$.State",
                "type": "expr",
            },
            {
                "source": {
                    "source": "2 if var1 != 1024 else 0",
                    "placeholder": {"var1": "$.data.result"},
                },
                "source_name": "current",
                "target": "$.State",
                "type": "expr",
            },
            {
                "source": {
                    "source": "$.path[0].status",
                    "mapping": {"ACTIVE": 1},
                    "default": 2,
                },
                "source_name": "current",
                "target": "$.State",
                "type": "map",
            },
        ],
    }
    warning_config = {
        "name": "syntec_alarm",
        "description": "syntec_alarm",
        "rules": [
            {
                "source": {
                    "source": "1 if len(var1) and var2 == 'ARMED' else 0",
                    "placeholder": {"var1": "$.data.alarm", "var2": "$.data.emg"},
                },
                "source_name": "current",
                "target": "$.State",
                "type": "expr",
            },
            {"source": "$.id", "target": "$.id", "type": "reference"},
            {
                "source": {
                    "source": "TIMESTAMP_TO_STR(var1)",
                    "placeholder": {"var1": "$.timestamp"},
                },
                "target": "$.CreateDate",
                "type": "expr",
            },
            {"source": {"source": "UUID1()"}, "target": "$.Uuid", "type": "expr"},
        ],
        "trigger": {
            "mode": "change",
            "type": "any",
            "exclude": ["$.CreateDate", "$.Uuid"],
        },
    }
    count_config = {
        "name": "syntec_count",
        "description": "syntec_count",
        "rules": [
            {
                "source": "$.data.part_count",
                "source_name": "current",
                "target": "$.Count",
                "type": "reference",
            },
            {"source": "$.id", "target": "$.id", "type": "reference"},
            {
                "source": {
                    "source": "TIMESTAMP_TO_STR(var1)",
                    "placeholder": {"var1": "$.timestamp"},
                },
                "target": "$.CreateDate",
                "type": "expr",
            },
            {"source": {"source": "UUID1()"}, "target": "$.Uuid", "type": "expr"},
        ],
        "trigger": {
            "mode": "change",
            "type": "any",
            "exclude": ["$.CreateDate", "$.Uuid"],
        },
    }
    config = count_config
    config = RuleModel(**config)
    plugin = DataMappingRule(config)
    print(plugin.transform(data, FlowRuleConfigModel(name="")))
    print(plugin._get_inbound_state(None).get("last_changed", False))
    data["timestamp"] = 1736231259.1262808
    data["data"]["part_count"] = 222
    data["data"]["path"][0]["status"] = "STOP"
    data["data"]["mode"] = "INTERUPT"
    print(plugin.transform(data, FlowRuleConfigModel(name="")))
    print(plugin._get_inbound_state(None).get("last_changed", False))


if __name__ == "__main__":
    main()
