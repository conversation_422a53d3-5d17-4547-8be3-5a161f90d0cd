from abc import ABC, ABCMeta, abstractmethod
from typing import (
    Any,
    Dict,
    Type,
    ClassVar,
    Dict,
    Set,
    Optional,
    List,
    Union,
    Tuple,
    Callable,
)
from enum import Enum, auto
import asyncio
from datetime import datetime
from dataclasses import dataclass
from loguru import logger
from beezer.type_model import RuleModel, FlowRuleConfigModel
import traceback
import time
from beezer.plugins.utils import report_plugin_status, report_plugin_data

INBOUND_INIT_DATA = {"timestamp": time.time(), "status": -1}


class PluginType(Enum):
    """插件类型枚举"""

    INBOUND = auto()  # 输入类型插件
    OUTBOUND = auto()  # 输出类型插件
    RULE = auto()  # 规则类型插件
    FLOW = auto()  # 数据流类型插件


class PluginMeta(ABCMeta):
    """插件元类，用于管理插件的注册和类型"""

    # 存储所有已注册的插件类
    plugins: ClassVar[Dict[str, Type["Plugin"]]] = {}
    # 按类型存储插件
    plugins_by_type: ClassVar[Dict[PluginType, Set[Type["Plugin"]]]] = {
        t: set() for t in PluginType
    }
    plugins_by_protocol: ClassVar[Dict[PluginType, Dict[str, Type["Plugin"]]]] = {
        t: {} for t in PluginType
    }  # 按类型和协议类型注册插件

    def __new__(mcs, name: str, bases: tuple, attrs: dict):
        cls = super().__new__(mcs, name, bases, attrs)
        if hasattr(cls, "plugin_type"):
            # 注册插件类
            mcs.plugins[name] = cls
            mcs.plugins_by_type[cls.plugin_type].add(cls)

            # 注册协议类型
            if hasattr(cls, "plugin_protocol") and cls.plugin_protocol:
                mcs.plugins_by_protocol[cls.plugin_type][cls.plugin_protocol] = cls
                print(f"Registered plugin {name} with protocol {cls.plugin_protocol}")
        return cls

    @classmethod
    def get_plugin(mcs, name: str) -> Type["Plugin"]:
        """通过名称获取插件类"""
        return mcs.plugins.get(name)

    @classmethod
    def get_plugins_by_type(mcs, plugin_type: PluginType) -> Set[Type["Plugin"]]:
        """获取指定类型的所有插件"""
        return mcs.plugins_by_type.get(plugin_type, set())

    @classmethod
    def get_plugin_by_protocol(
        mcs, plugin_type: PluginType, protocol: str
    ) -> Type["Plugin"]:
        """通过插件类型和协议类型获取插件类"""
        return mcs.plugins_by_protocol.get(plugin_type, {}).get(protocol)


class Plugin(ABC, metaclass=PluginMeta):
    """插件基类"""

    plugin_type: ClassVar[PluginType]
    plugin_protocol: ClassVar[str] = (
        ""  # 插件使用的协议类型，如'modbus'、'http_client'等
    )

    async def report_status(
        self, status: str, details: Optional[Dict[str, Any]] = None
    ) -> bool:
        """上报插件状态

        Args:
            status: 状态信息，例如 'running', 'error', 'idle' 等
            details: 详细状态信息，可选

        Returns:
            是否上报成功
        """
        if not hasattr(self, "config") or not hasattr(self.config, "id"):
            logger.warning("无法上报插件状态：插件配置缺少id")
            return False

        plugin_id = self.config.id
        plugin_type = self.__class__.__name__

        # 调用状态上报工具函数
        return await report_plugin_status(
            plugin_id=plugin_id, plugin_type=plugin_type, status=status, details=details
        )

    async def report_data(
        self, data_type: str, data: Any, metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """上报插件数据

        Args:
            data_type: 数据类型，例如 'input', 'output', 'processed' 等
            data: 实际数据
            metadata: 元数据信息，可选

        Returns:
            是否上报成功
        """
        if not hasattr(self, "config") or not hasattr(self.config, "id"):
            logger.debug("无法上报插件数据：插件配置缺少id")
            return False

        plugin_id = self.config.id
        plugin_type = (
            self.plugin_type.name.lower()
            if hasattr(self, "plugin_type")
            else self.__class__.__name__
        )

        # 调用数据上报工具函数
        return await report_plugin_data(
            plugin_id=plugin_id,
            plugin_type=plugin_type,
            data_type=data_type,
            data=data,
            metadata=metadata,
        )

    @abstractmethod
    async def execute(self, data: Any = None, config: Dict[str, Any] = None) -> Any:
        """执行插件功能

        Args:
            data: 输入数据
            config: 配置信息

        Returns:
            处理后的数据
        """
        pass


class InboundPlugin(Plugin):
    """数据输入插件基类"""

    plugin_type = PluginType.INBOUND

    def __init__(self, config: Dict[str, Any]):
        """初始化插件

        Args:
            config: 插件配置
        """
        self.config = config
        self._on_data: Optional[Callable[[Any], None]] = None
        self._running = False
        self._data_queue: Optional[asyncio.Queue] = None
        self._queue_task: Optional[asyncio.Task] = None
        # 默认队列最大长度，可通过配置修改
        self.max_queue_size = 2000

    async def start(self, on_data: Callable[[Any], None]):
        """启动数据采集

        Args:
            on_data: 数据回调函数，当有新数据时调用
        """
        self._on_data = on_data
        self._running = True
        self._data_queue = asyncio.Queue(maxsize=self.max_queue_size)
        self._queue_task = asyncio.create_task(self._process_queue())
        init_data = INBOUND_INIT_DATA.copy()
        init_data["timestamp"] = time.time()
        init_data["id"] = self.config.id
        await self.emit_data(init_data)

        # 子类可以重写此方法实现自己的采集逻辑
        if hasattr(self, "collect") and callable(self.collect):
            if asyncio.iscoroutinefunction(self.collect):
                asyncio.create_task(self.collect())
            else:
                self.collect()

    async def stop(self):
        """停止数据采集"""
        self._running = False
        if self._queue_task:
            self._queue_task.cancel()
            self._queue_task = None
        await self._clean_queue(self.max_queue_size)
        self._data_queue = None
        self._on_data = None
        if hasattr(self, "cleanup") and callable(self.cleanup):
            await self.cleanup()

    async def execute(self, data: Any = None, config: Dict[str, Any] = None) -> Any:
        """执行数据读取

        Args:
            data: 未使用
            config: 配置信息

        Returns:
            读取的数据
        """
        return await self.read(config or {})

    @abstractmethod
    async def read(self, config: Dict[str, Any]) -> Any:
        """实际的数据读取实现"""
        pass

    async def _process_queue(self):
        """处理数据队列的后台任务"""
        while self._running:
            try:
                data = await self._data_queue.get()
                if self._on_data:
                    logger.debug(f"Processing queued data: {data}")
                    try:
                        if asyncio.iscoroutinefunction(self._on_data):
                            await self._on_data(data)
                        else:
                            self._on_data(data)
                    except Exception as e:
                        logger.error(f"Error processing queued data: {e}")
                self._data_queue.task_done()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error processing queued data: {e}")

    async def _clean_queue(self, pop_size: int = 100):
        """清理队列中的旧数据"""
        if not self._data_queue:
            return

        # 删除指定数量的旧数据
        for _ in range(pop_size):
            try:
                self._data_queue.get_nowait()
                self._data_queue.task_done()
            except asyncio.QueueEmpty:
                break
        logger.warning(
            f"Queue cleanup completed, current size: {self._data_queue.qsize()}"
        )

    async def emit_data(self, data: Any):
        """发送数据到flow处理链

        Args:
            data: 要发送的数据
        """
        if not self._data_queue or not self._running:
            return

        try:
            # 检查队列是否已满
            if self._data_queue.full():
                logger.warning(
                    f"Queue is full (size: {self._data_queue.qsize()}), cleaning old data"
                )
                await self._clean_queue()

            # 将数据放入队列
            logger.debug(f"Queuing data: {data}")
            await self._data_queue.put(data)

            # 上报数据到监控系统
            await self.report_data(
                "input", data, {"queue_size": self._data_queue.qsize()}
            )
        except Exception as e:
            logger.error(f"Error queuing data: {e}")


class OutboundPlugin(Plugin):
    """数据输出插件基类"""

    plugin_type = PluginType.OUTBOUND

    def __init__(self, config):
        self.config = config
        # 注册可用的actions
        self.actions: Dict[str, Callable] = {
            "write": self.action_write  # 默认写入动作
        }

    async def execute(self, data: Any = None, action: str = None) -> Any:
        """执行数据写入

        Args:
            data: 要写入的数据
            config: 配置信息，包含:
                - action: str，要执行的动作名称
                - action_config: Dict，动作相关的配置

        Returns:
            写入结果

        Raises:
            ValueError: 如果指定的action不存在
        """

        action_name = action or "write"
        if action_name not in self.actions:
            raise ValueError(
                f"Action '{action_name}' not found in outbound plugin {self.__class__.__name__}. "
                f"Available actions: {list(self.actions.keys())}"
            )

        action_config = {}

        # 上报输出数据到监控系统
        await self.report_data("output", data, {"action": action_name})

        return await self.actions[action_name](data, action_config)

    async def write(self, data: Any, config: Dict[str, Any]) -> Any:
        """默认的数据写入实现

        Args:
            data: 要写入的数据
            config: 配置信息

        Returns:
            写入结果
        """
        return data

    async def action_write(self, data: Any, config: Dict[str, Any]) -> Any:
        """默认写入动作实现，调用write方法

        Args:
            data: 要写入的数据
            config: 配置信息

        Returns:
            写入结果
        """
        return await self.write(data, config)

    def register_action(self, name: str, action: Callable[[Any, Dict[str, Any]], Any]):
        """注册新的action

        Args:
            name: action名称
            action: action实现函数，接收data和config参数

        Raises:
            ValueError: 如果action名称已存在
        """
        if name in self.actions:
            raise ValueError(
                f"Action '{name}' already exists in outbound plugin {self.__class__.__name__}"
            )
        self.actions[name] = action


class RulePlugin(Plugin):
    """数据转换规则插件基类"""

    plugin_type = PluginType.RULE

    def __init__(self, config: RuleModel):
        self.config = config
        self._inbound_states = {}  # Track state per inbound source

    def _get_inbound_state(self, inbound_id: str) -> dict:
        """Get or create state for specific inbound source"""
        if inbound_id not in self._inbound_states:
            self._inbound_states[inbound_id] = {
                "last_data": {},
                "trigger_count": 0,
                "last_changed": False,
            }
        return self._inbound_states[inbound_id]

    def should_trigger(
        self, data: Any, trigger_config: "RuleTriggerModel", inbound_id: str = None
    ) -> bool:
        """判断是否应该触发outbound

        Args:
            data: 当前数据
            trigger_config: 触发配置
            inbound_id: 输入源ID，用于区分不同输入源

        Returns:
            是否应该触发
        """
        if inbound_id is None:
            # Fallback to default behavior if no inbound_id provided
            return True

        state = self._get_inbound_state(inbound_id)

        if trigger_config.mode == "interval":
            return True

        return state.get("last_changed", False)

    async def execute(
        self, data: Any, flow_rule_config: FlowRuleConfigModel, inbound_id: str = None
    ) -> tuple:
        """执行数据转换

        Args:
            data: 要转换的数据
            flow_rule_config: 规则配置
            inbound_id: 输入源ID

        Returns:
            (转换后的数据, 是否触发outbound)
        """

        transformed_data = self.transform(data, flow_rule_config, inbound_id)
        state = self._get_inbound_state(inbound_id) if inbound_id else None
        should_trigger = self.should_trigger(data, self.config.trigger, inbound_id)

        if state is not None:
            state["trigger_count"] += 1 if should_trigger else 0

        return transformed_data, should_trigger

    async def transform(self, data: Any, flow_rule_config: FlowRuleConfigModel) -> Any:
        """实际的数据转换实现

        Args:
            data: 要转换的数据

        Returns:
            转换后的数据
        """
        return data


class FlowPlugin(Plugin):
    """数据流插件基类"""

    plugin_type = PluginType.FLOW

    def __init__(self, flow_config: Dict[str, Any], app_config: Dict[str, Any]):
        self.flow_config = flow_config
        self.app_config = app_config
        self.inbounds: List[InboundPlugin] = []
        self.rules: List[
            Tuple[RulePlugin, Dict[str, Any]]
        ] = []  # (rule, rule_config)元组列表
        self.outbound: Optional[OutboundPlugin] = None
        self.outbound_config: Dict[str, Any] = {}  # outbound全局配置
        self.config: Dict[str, Any] = {}
        self._running = False

    def add_rule(self, rule: RulePlugin, rule_config: FlowRuleConfigModel):
        """添加规则插件

        Args:
            rule: 规则插件
            rule_config: 规则配置
        """
        self.rules.append((rule, rule_config))

    def set_outbound(
        self, outbound: OutboundPlugin, outbound_config: Dict[str, Any] = None
    ):
        """设置输出插件

        Args:
            outbound: 输出插件
            outbound_config: 输出配置，包含:
                - action: str，默认动作名称
                - action_config: Dict，默认动作配置
        """
        self.outbound = outbound
        self.outbound_config = outbound_config or {}

    async def start(self):
        """启动flow处理"""
        if self._running:
            return

        self._running = True
        # Start all inbounds
        for inbound in self.inbounds:
            if hasattr(inbound, "start"):

                async def callback(data, inbound=inbound):
                    await self._process_inbound_data(data, inbound)

                await inbound.start(callback)

    async def stop(self):
        """停止flow处理"""
        if not self._running:
            return

        self._running = False
        # Stop all inbounds
        for inbound in self.inbounds:
            if hasattr(inbound, "stop"):
                await inbound.stop()

    async def _process_inbound_data(self, data: Any, inbound: InboundPlugin):
        """处理输入数据

        Args:
            data: 输入数据
            inbound: 输入插件
        """
        if not self.outbound:
            logger.warning("No outbound configured for flow")
            return

        try:
            for rule, flow_rule_config in self.rules:
                transformed_data, should_trigger = await rule.execute(
                    data, flow_rule_config, inbound.config.id
                )
                if should_trigger:
                    logger.debug(
                        f"Triggering outbound with data: {flow_rule_config.action}, {transformed_data}"
                    )
                    await self.outbound.execute(
                        transformed_data, flow_rule_config.action
                    )
        except Exception as e:
            logger.error(f"Error processing data: {str(e)}\n{traceback.format_exc()}")


class RuleTriggerModel:
    def __init__(self, mode: str = "interval"):
        self.mode = mode
