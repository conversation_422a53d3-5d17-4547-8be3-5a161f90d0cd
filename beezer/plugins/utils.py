import aiohttp
import time
from loguru import logger
from typing import Dict, Any, Optional


def get_server_port() -> int:
    """获取服务器端口配置

    Returns:
        int: 服务器端口，默认为9999
    """
    try:
        # 尝试从Sanic应用获取配置
        from sanic import Sanic

        try:
            app = Sanic.get("beezer")
            if hasattr(app.config, "server_port"):
                return app.config.server_port
        except Exception:
            pass

        # 如果无法获取Sanic配置，尝试直接读取配置文件
        import yaml
        from beezer.type_model import ConfigModel

        config_paths = ["/app/config.yaml", "config.yaml"]
        for config_path in config_paths:
            try:
                with open(config_path, "r", encoding="utf-8") as f:
                    config_data = yaml.safe_load(f)
                config = ConfigModel.model_validate(config_data)
                return config.server_port
            except Exception:
                continue

    except Exception as e:
        logger.warning(f"无法获取服务器端口配置，使用默认值9999: {e}")

    # 默认端口
    return 9999


async def report_plugin_status(
    plugin_id: str,
    plugin_type: str,
    status: str,
    details: Optional[Dict[str, Any]] = None,
    api_url: Optional[str] = None,
) -> bool:
    """向main服务上报插件状态

    Args:
        plugin_id: 插件ID
        plugin_type: 插件类型
        status: 状态信息
        details: 详细状态信息
        api_url: 状态上报API地址，如果为None则自动构建

    Returns:
        是否上报成功
    """
    try:
        # 如果没有提供api_url，则根据配置自动构建
        if api_url is None:
            server_port = get_server_port()
            api_url = f"http://localhost:{server_port}/api/plugin/status"

        data = {
            "plugin_id": plugin_id,
            "plugin_type": plugin_type,
            "status": status,
            "timestamp": time.time(),
        }

        if details:
            data["details"] = details

        async with aiohttp.ClientSession() as session:
            async with session.post(api_url, json=data) as response:
                if response.status == 200:
                    response_data = await response.json()
                    return response_data.get("success", False)
                else:
                    error_text = await response.text()
                    logger.error(f"上报插件状态失败: {error_text}")
                    return False
    except Exception as e:
        logger.error(f"上报插件状态时发生错误: {str(e)}")
        return False


async def report_plugin_data(
    plugin_id: str,
    plugin_type: str,
    data_type: str,
    data: Any,
    metadata: Optional[Dict[str, Any]] = None,
    api_url: Optional[str] = None,
) -> bool:
    """向main服务上报插件数据

    Args:
        plugin_id: 插件ID
        plugin_type: 插件类型 (inbound/outbound/rule)
        data_type: 数据类型 (input/output/processed)
        data: 实际数据
        metadata: 元数据信息
        api_url: 数据上报API地址，如果为None则自动构建

    Returns:
        是否上报成功
    """
    try:
        # 如果没有提供api_url，则根据配置自动构建
        if api_url is None:
            server_port = get_server_port()
            api_url = f"http://localhost:{server_port}/api/plugin/data"

        payload = {
            "plugin_id": plugin_id,
            "plugin_type": plugin_type,
            "data_type": data_type,
            "data": data,
            "timestamp": time.time(),
        }

        if metadata:
            payload["metadata"] = metadata

        async with aiohttp.ClientSession() as session:
            async with session.post(api_url, json=payload) as response:
                if response.status == 200:
                    response_data = await response.json()
                    return response_data.get("success", False)
                else:
                    error_text = await response.text()
                    logger.debug(f"上报插件数据失败: {error_text}")
                    return False
    except Exception as e:
        logger.debug(f"上报插件数据时发生错误: {str(e)}")
        return False
