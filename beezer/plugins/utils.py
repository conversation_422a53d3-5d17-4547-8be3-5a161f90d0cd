import aiohttp
import asyncio
import time
from loguru import logger
from typing import Dict, Any, Optional


async def report_plugin_status(
    plugin_id: str,
    plugin_type: str,
    status: str,
    details: Optional[Dict[str, Any]] = None,
    api_url: str = "http://localhost:8000/api/plugin/status",
) -> bool:
    """向main服务上报插件状态

    Args:
        plugin_id: 插件ID
        plugin_type: 插件类型
        status: 状态信息
        details: 详细状态信息
        api_url: 状态上报API地址

    Returns:
        是否上报成功
    """
    try:
        data = {
            "plugin_id": plugin_id,
            "plugin_type": plugin_type,
            "status": status,
            "timestamp": time.time(),
        }

        if details:
            data["details"] = details

        async with aiohttp.ClientSession() as session:
            async with session.post(api_url, json=data) as response:
                if response.status == 200:
                    response_data = await response.json()
                    return response_data.get("success", False)
                else:
                    error_text = await response.text()
                    logger.error(f"上报插件状态失败: {error_text}")
                    return False
    except Exception as e:
        logger.error(f"上报插件状态时发生错误: {str(e)}")
        return False


async def report_plugin_data(
    plugin_id: str,
    plugin_type: str,
    data_type: str,
    data: Any,
    metadata: Optional[Dict[str, Any]] = None,
    api_url: str = "http://localhost:8000/api/plugin/data",
) -> bool:
    """向main服务上报插件数据

    Args:
        plugin_id: 插件ID
        plugin_type: 插件类型 (inbound/outbound/rule)
        data_type: 数据类型 (input/output/processed)
        data: 实际数据
        metadata: 元数据信息
        api_url: 数据上报API地址

    Returns:
        是否上报成功
    """
    try:
        payload = {
            "plugin_id": plugin_id,
            "plugin_type": plugin_type,
            "data_type": data_type,
            "data": data,
            "timestamp": time.time(),
        }

        if metadata:
            payload["metadata"] = metadata

        async with aiohttp.ClientSession() as session:
            async with session.post(api_url, json=payload) as response:
                if response.status == 200:
                    response_data = await response.json()
                    return response_data.get("success", False)
                else:
                    error_text = await response.text()
                    logger.debug(f"上报插件数据失败: {error_text}")
                    return False
    except Exception as e:
        logger.debug(f"上报插件数据时发生错误: {str(e)}")
        return False
