"""WebSocket Outbound 插件，用于通过 WebSocket 发送数据。

这个插件会将数据通过 WebSocket 连接发送到指定的服务器。
"""

import json
import time
import asyncio
from typing import Dict, Any
from loguru import logger
import websockets
from websockets.exceptions import ConnectionClosed, InvalidURI

from beezer.plugins._base import OutboundPlugin
from beezer.type_model import WebSocketOutboundConfig


class WebSocketOutbound(OutboundPlugin):
    """WebSocket 数据输出插件。"""
    
    plugin_protocol = "websocket"
    
    def __init__(self, config: Dict[str, Any]):
        validated_config = WebSocketOutboundConfig.model_validate(config)
        super().__init__(validated_config)
        self.config = validated_config
        self._websocket = None
        self._reconnect_attempts = 0
        self._last_ping = 0
        self._send_counter = 0
        
        # 注册动作
        self.register_action("send", self.action_send)
        self.register_action("broadcast", self.action_broadcast)
        
        logger.info(f"WebSocket Outbound {self.config.id} 已初始化")
    
    async def _connect(self):
        """建立 WebSocket 连接。"""
        try:
            self._websocket = await websockets.connect(self.config.url)
            self._reconnect_attempts = 0
            logger.info(f"WebSocket 连接已建立: {self.config.url}")
            
            await self.report_status("connected", {
                "url": self.config.url,
                "reconnect_attempts": self._reconnect_attempts,
            })
            
        except Exception as e:
            logger.error(f"WebSocket 连接失败: {e}")
            await self.report_status("error", {
                "error_message": str(e),
                "url": self.config.url,
                "reconnect_attempts": self._reconnect_attempts,
            })
            raise
    
    async def _ensure_connected(self):
        """确保 WebSocket 连接可用。"""
        if self._websocket is None or self._websocket.closed:
            if self._reconnect_attempts < self.config.max_reconnect_attempts:
                self._reconnect_attempts += 1
                logger.info(f"尝试重连 WebSocket ({self._reconnect_attempts}/{self.config.max_reconnect_attempts})")
                
                await asyncio.sleep(self.config.reconnect_interval)
                await self._connect()
            else:
                raise ConnectionError(f"WebSocket 重连失败，已达到最大重试次数: {self.config.max_reconnect_attempts}")
    
    async def _send_ping(self):
        """发送 ping 消息。"""
        current_time = time.time()
        if current_time - self._last_ping > self.config.ping_interval:
            try:
                if self._websocket and not self._websocket.closed:
                    await self._websocket.ping()
                    self._last_ping = current_time
                    logger.debug("WebSocket ping 发送成功")
            except Exception as e:
                logger.warning(f"WebSocket ping 失败: {e}")
    
    async def write(self, data: Any, config: Dict[str, Any]) -> Any:
        """默认写入实现。"""
        return await self.action_send(data, config)
    
    async def action_send(self, data: Any, config: Dict[str, Any]) -> Any:
        """发送动作。"""
        try:
            await self.report_status("running", {
                "action": "send",
                "send_count": self._send_counter,
                "last_send_time": time.strftime("%H:%M:%S"),
            })
            
            await self._ensure_connected()
            await self._send_ping()
            
            # 格式化数据为 JSON
            message = json.dumps(data, ensure_ascii=False)
            
            # 发送数据
            await self._websocket.send(message)
            self._send_counter += 1
            
            logger.debug(f"WebSocket 数据发送成功: {len(message)} 字符")
            
            # 定期上报状态
            if self._send_counter % 10 == 0:
                await self.report_status("running", {
                    "total_sends": self._send_counter,
                    "url": self.config.url,
                    "last_action": "send",
                })
            
            return {"success": True, "send_count": self._send_counter}
            
        except ConnectionClosed:
            logger.warning("WebSocket 连接已关闭，尝试重连")
            self._websocket = None
            await self.report_status("disconnected", {
                "reason": "connection_closed",
                "send_count": self._send_counter,
            })
            return {"success": False, "error": "connection_closed"}
            
        except Exception as e:
            await self.report_status("error", {
                "error_message": str(e),
                "action": "send",
            })
            logger.error(f"WebSocket 发送失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def action_broadcast(self, data: Any, config: Dict[str, Any]) -> Any:
        """广播动作（与 send 相同，但可以扩展为多连接广播）。"""
        try:
            await self.report_status("running", {
                "action": "broadcast",
                "send_count": self._send_counter,
            })
            
            # 目前与 send 相同，未来可以扩展为多连接广播
            result = await self.action_send(data, config)
            
            return result
            
        except Exception as e:
            await self.report_status("error", {
                "error_message": str(e),
                "action": "broadcast",
            })
            logger.error(f"WebSocket 广播失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def close(self):
        """关闭 WebSocket 连接。"""
        if self._websocket and not self._websocket.closed:
            await self._websocket.close()
            logger.info("WebSocket 连接已关闭")
            
            await self.report_status("disconnected", {
                "reason": "manual_close",
                "total_sends": self._send_counter,
            })
