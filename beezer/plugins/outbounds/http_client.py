"""
HTTP Client Outbound Plugins
"""

from typing import Any, Dict, List, Literal
import random
from decimal import Decimal
import time
import asyncio
from enum import Enum
from pydantic import BaseModel
from aiohttp import ClientSession, ClientTimeout
from loguru import logger
import orjson
import traceback
from beezer.plugins._base import OutboundPlugin
from beezer.type_model import XinHeYunOutboundConfig, CustomIOTOutboundConfig
from urllib.parse import urlparse, ParseResult


class MachineStatus(int, Enum):
    关机 = 1
    空闲 = 2
    调机 = 3
    生产 = 4
    故障 = 5


class ConnectionStatus(int, Enum):
    connect_success = 1
    connect_fail = 2


class AlarmInfo(BaseModel):
    alarmId: int
    alarmMessage: str


class StatusInputSchema(BaseModel):
    gatewayId: str
    runtime: int
    machineStatus: Literal[1, 2, 3, 4, 5]
    machineFailureCode: int = 0
    gatewayStatus: Literal[1, 2]
    gatewayFailureCode: int = 0
    runSpeed: Decimal = Decimal(0)
    planQty: Decimal = Decimal(0)
    actualQty: Decimal
    runJson: str | None = None
    alarms: List[AlarmInfo] | None = None


class XinHeYunOutbound(OutboundPlugin):
    """新禾云数据输出插件"""

    plugin_protocol = "xinheyun"

    def __init__(self, config: Dict[str, Any]):
        validated_config = XinHeYunOutboundConfig.model_validate(config)
        super().__init__(validated_config)
        self.config = validated_config
        self.access_token: str | None = None
        self.access_token_timeout: int = 0
        self.refresh_token: str | None = None
        self.refresh_token_timeout: int = 0

        # 注册状态上报动作
        self.register_action("status", self.action_status)

    async def post_data(self, uri: str, data: dict) -> dict:
        """发送HTTP POST请求

        Args:
            uri: API路径
            data: 请求数据

        Returns:
            响应数据
        """
        async with ClientSession() as session:
            if self.refresh_token is None or self.refresh_token_timeout < time.time():
                await self.login()
            if self.access_token is None or self.access_token_timeout < time.time():
                await self.refresh_access_token()

            headers = {"Authorization": f"Bearer {self.access_token}"}
            async with session.post(
                f"{self.config.api_host}{uri}", headers=headers, json=data
            ) as resp:
                return await resp.json()

    async def login(self):
        """登录获取token"""
        async with ClientSession() as session:
            data = {"appKey": self.config.api_key, "appSecret": self.config.api_secret}
            async with session.post(
                f"{self.config.api_host}/api/open/token", json=data
            ) as resp:
                result = await resp.json()
                logger.debug(result)
                self.access_token = result.get("accessToken")
                self.refresh_token = result.get("refreshToken")
                self.access_token_timeout = (
                    result.get("accessTokenExpireIn", 0) + time.time() - 120
                )
                self.refresh_token_timeout = (
                    result.get("refreshTokenExpireIn", 0) + time.time() - 600
                )

    async def refresh_access_token(self):
        """刷新access token"""
        async with ClientSession() as session:
            data = {"refreshToken": self.refresh_token}
            async with session.post(
                f"{self.config.api_host}/api/open/token", json=data
            ) as resp:
                result = await resp.json()
                logger.debug(result)
                self.access_token = result.get("accessToken")
                self.refresh_token = result.get("refreshToken")
                self.access_token_timeout = (
                    result.get("accessTokenExpireIn", 0) + time.time() - 120
                )
                self.refresh_token_timeout = (
                    result.get("refreshTokenExpireIn", 0) + time.time() - 600
                )

    async def action_status(self, data: Any, config: Dict[str, Any]) -> Any:
        """状态上报动作

        Args:
            data: 状态数据
            config: 动作配置

        Returns:
            上报结果
        """
        logger.debug(f"Status data: {data}")
        result = await self.post_data("/api/msg/v1/m/status", data)
        logger.debug(f"Status result: {result}")
        return result


class CustomIOTOutbound(OutboundPlugin):
    """自定义IOT数据输出插件"""

    plugin_protocol = "custom_ioteq"

    def __init__(self, config: Dict[str, Any]):
        validated_config = CustomIOTOutboundConfig.model_validate(config)
        super().__init__(validated_config)
        self.config = validated_config

        # 注册支持的动作
        self.register_action("IOTEqStatues", self.action_status)
        self.register_action("IOTEqWarning", self.action_warning)
        self.register_action("IOTEqProduceCount", self.action_produce_count)
        self.register_action("IOTEqMachiningParams", self.action_machining_params)

        # 记录每种action是否已经执行过的状态
        self._action_sent = {}
        # 互斥锁，用于确保状态更新的线程安全
        self._action_lock = asyncio.Lock()
        self.register_action("IotParametersGet", self.action_iot_parameters_get)

    async def post_data(self, uri: str, data: dict, host: str | None = None) -> dict:
        """发送HTTP POST请求

        Args:
            uri: API路径
            data: 请求数据

        Returns:
            响应数据
        """
        # logger.debug(f"Post data: {uri} {data}")
        # return {"code": 200}
        if host is None:
            host = self.config.api_host
        async with ClientSession(timeout=ClientTimeout(total=3)) as session:
            async with session.post(f"{host}{uri}", json=data) as resp:
                if resp.headers.get("Content-type") == "application/json":
                    return await resp.json()
                else:
                    return orjson.loads(await resp.text())

    async def _do_action_with_retry(
        self, action: str, data: Any, retries: int = 3, host: str | None = None
    ) -> Any:
        """执行动作(带重试)

        Args:
            action: 动作名称
            data: 动作数据
            retries: 重试次数

        Returns:
            执行结果
        """
        # 检查该action是否是首次发送
        is_first_send = False
        async with self._action_lock:
            if action not in self._action_sent:
                is_first_send = True
                self._action_sent[action] = True

        # 如果是首次发送且配置了初始延迟，则添加随机延迟
        if is_first_send and self.config.initial_delay:
            delay_time = random.uniform(
                self.config.initial_delay_min, self.config.initial_delay_max
            )
            logger.debug(
                f"Adding initial delay of {delay_time:.2f}s for first time action {action}"
            )
            await asyncio.sleep(delay_time)

        try:
            res = await self.post_data(action, data, host)
            logger.debug(f"Action result: {action} {data} {res}")
            if not res or res.get("code") != 200 or res.get("Code") != 200:
                if retries > 0:
                    # 计算带jitter的重试延迟时间
                    retry_delay = random.uniform(
                        self.config.retry_delay, self.config.retry_delay_max
                    )
                    logger.debug(
                        f"Retrying action {action} in {retry_delay:.2f}s ({retries} attempts left)"
                    )
                    # 后台重试，不等待结果
                    loop = asyncio.get_event_loop()
                    loop.call_later(
                        retry_delay,
                        lambda: asyncio.create_task(
                            self._do_action_with_retry(action, data, retries - 1, host)
                        ),
                    )
                    return None
                else:
                    logger.error(
                        f"Failed to execute action {action} after {self.config.retries} retries"
                    )
                    return None
            return res
        except Exception as e:
            logger.error(
                f"Error retrying {retries} executing action {action} {data.get('id')} {data.get('Uuid')}: {traceback.format_exc()}"
            )
            if retries > 0:
                # 计算带jitter的重试延迟时间
                retry_delay = random.uniform(
                    self.config.retry_delay, self.config.retry_delay_max
                )
                logger.debug(
                    f"Retrying action {action} in {retry_delay:.2f}s after error ({retries} attempts left)"
                )
                # 后台重试，不等待结果
                loop = asyncio.get_event_loop()
                loop.call_later(
                    retry_delay,
                    lambda: asyncio.create_task(
                        self._do_action_with_retry(action, data, retries - 1, host)
                    ),
                )
                return None
            return None

    async def action_status(self, data: Any, config: Dict[str, Any]) -> Any:
        """设备状态上报

        Args:
            data: 状态数据
            config: 动作配置

        Returns:
            上报结果
        """
        return await self._do_action_with_retry(
            "IOTEqStatues", data, self.config.retries
        )

    async def action_warning(self, data: Any, config: Dict[str, Any]) -> Any:
        """设备告警上报

        Args:
            data: 告警数据
            config: 动作配置

        Returns:
            上报结果
        """
        return await self._do_action_with_retry(
            "IOTEqWarning", data, self.config.retries
        )

    async def action_produce_count(self, data: Any, config: Dict[str, Any]) -> Any:
        """产量上报

        Args:
            data: 产量数据
            config: 动作配置

        Returns:
            上报结果
        """
        return await self._do_action_with_retry(
            "IOTEqProduceCount", data, self.config.retries
        )

    async def action_machining_params(self, data: Any, config: Dict[str, Any]) -> Any:
        """加工参数上报

        Args:
            data: 加工参数数据
            config: 动作配置

        Returns:
            上报结果
        """
        return await self._do_action_with_retry(
            "IOTEqMachiningParams", data, self.config.retries
        )

    async def action_iot_parameters_get(
        self, data: Any, config: Dict[str, Any], path: str = "/eq/IotParameters/"
    ) -> Any:
        """设备参数获取

        Args:
            data: 参数数据
            config: 动作配置

        Returns:
            上报结果
        """
        parsed: ParseResult = urlparse(self.config.api_host)
        parsed = parsed._replace(path=path)
        return await self._do_action_with_retry(
            "IotParametersGet", data, self.config.retries, host=parsed.geturl()
        )
