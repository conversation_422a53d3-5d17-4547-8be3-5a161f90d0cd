from pydantic import (
    BaseModel,
    IPvAnyAddress,
    field_validator,
    FieldValidationInfo,
    Field,
)
from enum import Enum
from typing import Literal, List, Any, Dict


class InboundTypes(str, Enum):
    Modbus = "modbus"
    Focas = "focas"
    HttpClient = "http_client"
    Fanuc = "fanuc"
    Siemens = "siemens"
    Mock = "mock"


class RuleTypes(str, Enum):
    DataMapping = "data_mapping"
    DataAggregation = "data_aggregation"


class OutboundTypes(str, Enum):
    Print = "print"
    XinHeYun = "xinheyun"
    CustomIOT = "custom_ioteq"
    WebSocket = "websocket"


class ModbusPointValueEnum(BaseModel):
    value: int | float
    alias: str | int | float | bool | None = None


class ModbusPoint(BaseModel):
    address: int
    count: int
    addr_type: Literal["holding_register"] = "holding_register"
    value_type: Literal[
        "int16",
        "int32",
        "float32",
        "uint16",
        "uint32",
        "float",
        "double",
        "char",
        "string",
    ]
    byte_order: Literal["big", "little"] = "little"
    name: str
    enum: List[ModbusPointValueEnum] | None = None


class ModbusConfig(BaseModel):
    type: Literal[InboundTypes.Modbus]
    proto: Literal["tcp", "rtu"] = "tcp"
    connect: Literal["tcp", "serial"] = "tcp"
    ip: IPvAnyAddress | None = None
    id: str
    port: int | None = None
    slave: int = 1  # Modbus slave address
    points: List[ModbusPoint] | None = None
    interval: int = 1

    @field_validator("ip", "port", mode="before")
    def check_ip_port(cls, v: Any, info: FieldValidationInfo):
        if info.data.get("connect") == "tcp":
            if info.field_name == "ip" and v is None:
                raise ValueError("IP must be provided when connect is tcp")
            if info.field_name == "port" and v is None:
                raise ValueError("Port must be provided when connect is tcp")
        return v


class FocasConfig(BaseModel):
    type: Literal[InboundTypes.Focas]


class HttpRequestConfig(BaseModel):
    method: Literal["GET", "POST", "PUT", "DELETE"]
    name: str
    url: str
    headers: Dict[str, str] = Field(default_factory=dict)
    params: Dict[str, str] = Field(default_factory=dict)
    data: Any = None
    content_type: str | None = None


class HttpClientConfig(BaseModel):
    type: Literal[InboundTypes.HttpClient]
    id: str
    interval: int = 1
    requests: List[HttpRequestConfig]


class FanucConfig(BaseModel):
    type: Literal[InboundTypes.Fanuc]
    id: str
    ip: IPvAnyAddress
    port: int = 8193
    timeout: int = 10
    interval: int = 1


class SiemensConfig(BaseModel):
    type: Literal[InboundTypes.Siemens]
    id: str
    model: Literal["828D", "840D", "BCNetS7"] = "828D"
    ip: IPvAnyAddress
    port: int = 102
    interval: int = 1
    # 可选的配置选项，用于存储各种地址
    param_config: Dict[str, Any] = Field(default_factory=dict)


class MockConfig(BaseModel):
    type: Literal[InboundTypes.Mock]
    id: str
    interval: int = 2  # 数据生成间隔（秒）
    data_type: Literal["sensor", "machine", "modbus", "generic"] = "sensor"  # 数据类型
    error_rate: float = 0.1  # 错误率 (0-1)


class PrintOutboundConfig(BaseModel):
    type: Literal[OutboundTypes.Print]
    id: str
    format: Literal["json", "simple", "detailed"] = "json"  # 输出格式
    prefix: str = "[PRINT]"  # 输出前缀
    max_length: int = 500  # 最大输出长度


class XinHeYunOutboundConfig(BaseModel):
    type: Literal[OutboundTypes.XinHeYun]
    id: str
    api_host: str
    api_key: str
    api_secret: str


class CustomIOTOutboundConfig(BaseModel):
    type: Literal[OutboundTypes.CustomIOT]
    id: str
    api_host: str
    odbc_url: str = Field(
        description=(
            "mssql+pyodbc://用户名:密码@服务器地址/数据库名?driver=ODBC+Driver+17+for+SQL+Server"
        )
    )
    retries: int = 3
    retry_delay: int = 30
    retry_delay_max: int = 60  # 重试延迟最大值（秒），用于jitter
    initial_delay: bool = True  # 是否在第一次发送时添加延迟
    initial_delay_min: int = 0  # 初始延迟最小值（秒）
    initial_delay_max: int = 10  # 初始延迟最大值（秒）


class WebSocketOutboundConfig(BaseModel):
    type: Literal[OutboundTypes.WebSocket]
    id: str
    url: str
    reconnect_interval: int = 5  # 重连间隔（秒）
    max_reconnect_attempts: int = 10  # 最大重连次数
    ping_interval: int = 30  # ping间隔（秒）


InboundConfig = (
    ModbusConfig
    | FocasConfig
    | HttpClientConfig
    | FanucConfig
    | SiemensConfig
    | MockConfig
)


OutboundConfig = (
    PrintOutboundConfig
    | XinHeYunOutboundConfig
    | CustomIOTOutboundConfig
    | WebSocketOutboundConfig
)


def get_inbound_config_classes():
    """自动获取 InboundTypes 到配置类的映射。

    通过检查配置类的 type 字段的 Literal 值来建立映射关系。

    Returns:
        Dict[InboundTypes, type]: InboundTypes 枚举值到配置类的映射
    """
    import inspect

    # 获取当前模块中所有的类
    current_module = inspect.getmodule(inspect.currentframe())
    config_classes = {}

    # 遍历模块中的所有类
    for name, obj in inspect.getmembers(current_module, inspect.isclass):
        # 检查是否是 BaseModel 的子类且有 type 字段
        if (
            hasattr(obj, "model_fields")
            and "type" in obj.model_fields
            and obj != BaseModel
        ):
            try:
                # 获取 type 字段的注解
                type_field = obj.model_fields["type"]
                annotation = type_field.annotation

                # 检查是否是 Literal 类型
                if (
                    hasattr(annotation, "__origin__")
                    and annotation.__origin__ is Literal
                ):
                    # 获取 Literal 的值
                    literal_value = annotation.__args__[0]

                    # 检查是否是 InboundTypes 的成员
                    if isinstance(literal_value, InboundTypes):
                        config_classes[literal_value] = obj

            except Exception:
                # 如果解析失败，跳过这个类
                continue

    return config_classes


def get_outbound_config_classes():
    """自动获取 OutboundTypes 到配置类的映射。

    通过检查配置类的 type 字段的 Literal 值来建立映射关系。

    Returns:
        Dict[OutboundTypes, type]: OutboundTypes 枚举值到配置类的映射
    """
    import inspect

    # 获取当前模块中所有的类
    current_module = inspect.getmodule(inspect.currentframe())
    config_classes = {}

    # 遍历模块中的所有类
    for name, obj in inspect.getmembers(current_module, inspect.isclass):
        # 检查是否是 BaseModel 的子类且有 type 字段
        if (
            hasattr(obj, "model_fields")
            and "type" in obj.model_fields
            and obj != BaseModel
        ):
            try:
                # 获取 type 字段的注解
                type_field = obj.model_fields["type"]
                annotation = type_field.annotation

                # 检查是否是 Literal 类型
                if (
                    hasattr(annotation, "__origin__")
                    and annotation.__origin__ is Literal
                ):
                    # 获取 Literal 的值
                    literal_value = annotation.__args__[0]

                    # 检查是否是 OutboundTypes 的成员
                    if isinstance(literal_value, OutboundTypes):
                        config_classes[literal_value] = obj

            except Exception:
                # 如果解析失败，跳过这个类
                continue

    return config_classes


class OutBoundInputModel(BaseModel):
    name: str
    input_schema: Dict[Any, Any]


class InputMsgModel(BaseModel):
    input_type: str | None = "_"
    data: Any


class OutBoundModel(BaseModel):
    type: str
    config: Dict[str, Any]
    # inputs: List[Dict[Literal["name", "rule"], str]]

    async def do_action(self, message: InputMsgModel) -> None:
        pass


class RuleItemExprSourceModel(BaseModel):
    source: str
    placeholder: Dict[str, str] = Field(default_factory=dict)
    mapping: Dict[Any, Any] = Field(default_factory=dict)
    default: Any = None


class RuleItemModel(BaseModel):
    source_name: str | None = None
    source: str | RuleItemExprSourceModel
    target_name: str | None = None
    target: str
    type: Literal["reference", "fixed", "expr", "map"]


class RuleTriggerModel(BaseModel):
    mode: Literal["change", "interval"] = "change"
    type: Literal["any", "all"] = "any"
    exclude: List[str] = Field(default_factory=list)


class RuleModel(BaseModel):
    name: str
    rules: List[RuleItemModel]
    trigger: RuleTriggerModel = Field(default_factory=lambda: RuleTriggerModel())


class MergeFieldModel(BaseModel):
    """合并字段配置模型"""

    target: str = Field(description="目标字段路径，如 $.combined_status")
    strategy: Literal[
        "sum", "average", "latest", "majority_vote", "min", "max", "concat"
    ] = Field(description="合并策略")
    sources: List[str] = Field(
        description="源字段路径列表，如 [$.status, $.status, $.status]"
    )
    default_value: Any = Field(default=None, description="默认值，当源数据不可用时使用")


class MergeStrategyModel(BaseModel):
    """数据合并策略配置模型"""

    type: Literal["aggregate", "latest", "conditional"] = Field(
        default="aggregate", description="合并类型"
    )
    sources: List[str] = Field(description="参与合并的inbound ID列表")
    trigger_condition: Literal["all", "any", "majority", "custom"] = Field(
        default="any", description="触发条件"
    )
    timeout: int = Field(default=30, description="等待超时时间（秒）")
    merge_rules: List[MergeFieldModel] = Field(
        default_factory=list, description="字段合并规则列表"
    )
    custom_condition: str | None = Field(
        default=None, description="自定义触发条件表达式"
    )


class FlowRuleConfigModel(BaseModel):
    name: str
    trigger: List[str] | None = Field(
        default=None, description="the inbound sources to be triggered"
    )
    action: str | None = Field(
        default=None, description="The outbound action to be executed"
    )
    merge_strategy: MergeStrategyModel | None = Field(
        default=None, description="数据合并策略配置，如果配置则启用多inbound合并"
    )

    @field_validator("trigger", "merge_strategy", mode="before")
    def handle_none_values(cls, v):
        """Handle explicit None values from YAML"""
        if v is None or (isinstance(v, str) and v.lower() == "none"):
            return None
        return v


class FlowModel(BaseModel):
    """Flow model for connecting inbounds to outbound through rules"""

    name: str
    inbounds: List[str | int]
    outbound: str
    rules: List[FlowRuleConfigModel]


class AppConfigModel(BaseModel):
    name: str
    type: str
    args: List[Any] = Field(default_factory=list)


class ConfigModel(BaseModel):
    apps: List[AppConfigModel]
    inbounds: Dict[str | int, InboundConfig]
    outbounds: Dict[str, OutboundConfig]
    rules: List[RuleModel]
    flows: List[FlowModel]
    server_port: int = 9999
    version: int = 1

    @field_validator("server_port", "version", mode="before")
    def convert_string_to_int(cls, v):
        """Convert string values to integers"""
        if isinstance(v, str):
            try:
                return int(v)
            except ValueError:
                raise ValueError(f"Cannot convert '{v}' to integer")
        return v
