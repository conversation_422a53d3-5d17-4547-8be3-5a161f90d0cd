# Beezer 数据监控测试配置文件
# 使用 Mock Inbound 和 Print Outbound 进行测试

apps: []

inbounds:
  mock_sensor_001:
    type: mock
    id: mock_sensor_001
    interval: 3
    data_type: sensor
    error_rate: 0.05

outbounds:
  print_sensor:
    type: print
    config:
      id: print_sensor
      format: json
      prefix: "[SENSOR]"
      max_length: 300

rules:
  - name: sensor_mapping
    rules:
      - source_name: None
        source: $.id
        target_name: None
        target: $.device_id
        type: reference
      - source_name: None
        source: $.timestamp
        target_name: None
        target: $.timestamp
        type: reference
      - source_name: None
        source: $.sensors.temperature
        target_name: None
        target: $.temperature
        type: reference
    trigger:
      mode: interval
      type: any
      exclude: []

flows:
  - name: sensor_flow
    inbounds:
      - mock_sensor_001
    outbound: print_sensor
    rules:
      - name: sensor_mapping
        trigger: []
        action: print
        merge_strategy: {}

server_port: 8000
version: 1
