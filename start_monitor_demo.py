#!/usr/bin/env python3
"""启动数据监控演示的脚本。

这个脚本会启动Beezer主服务和GUI应用，并运行测试数据生成器。
"""

import asyncio
import subprocess
import time
import signal
import sys
from loguru import logger


class MonitorDemo:
    """数据监控演示管理器。"""
    
    def __init__(self):
        self.main_process = None
        self.gui_process = None
        self.test_process = None
        self.running = False
    
    async def start_main_service(self):
        """启动Beezer主服务。"""
        logger.info("启动Beezer主服务...")
        try:
            self.main_process = subprocess.Popen(
                ["python", "-m", "beezer.main"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
            )
            
            # 等待服务启动
            await asyncio.sleep(3)
            
            if self.main_process.poll() is None:
                logger.info(f"Beezer主服务启动成功, PID: {self.main_process.pid}")
                return True
            else:
                stdout, stderr = self.main_process.communicate()
                logger.error(f"Beezer主服务启动失败: {stderr}")
                return False
        except Exception as e:
            logger.error(f"启动Beezer主服务时出错: {e}")
            return False
    
    async def start_gui_service(self):
        """启动GUI服务。"""
        logger.info("启动GUI服务...")
        try:
            self.gui_process = subprocess.Popen(
                ["python", "-m", "beezer.gui"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
            )
            
            # 等待GUI启动
            await asyncio.sleep(3)
            
            if self.gui_process.poll() is None:
                logger.info(f"GUI服务启动成功, PID: {self.gui_process.pid}")
                logger.info("GUI访问地址: http://localhost:8081")
                return True
            else:
                stdout, stderr = self.gui_process.communicate()
                logger.error(f"GUI服务启动失败: {stderr}")
                return False
        except Exception as e:
            logger.error(f"启动GUI服务时出错: {e}")
            return False
    
    async def start_test_data(self):
        """启动测试数据生成器。"""
        logger.info("启动测试数据生成器...")
        try:
            self.test_process = subprocess.Popen(
                ["python", "test_monitor_api.py"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
            )
            
            # 等待测试启动
            await asyncio.sleep(2)
            
            if self.test_process.poll() is None:
                logger.info(f"测试数据生成器启动成功, PID: {self.test_process.pid}")
                return True
            else:
                stdout, stderr = self.test_process.communicate()
                logger.error(f"测试数据生成器启动失败: {stderr}")
                return False
        except Exception as e:
            logger.error(f"启动测试数据生成器时出错: {e}")
            return False
    
    async def start_all(self):
        """启动所有服务。"""
        logger.info("=== 启动数据监控演示 ===")
        
        # 启动主服务
        if not await self.start_main_service():
            logger.error("主服务启动失败，退出")
            return False
        
        # 启动GUI服务
        if not await self.start_gui_service():
            logger.error("GUI服务启动失败，退出")
            await self.stop_all()
            return False
        
        # 等待一下，然后启动测试数据
        await asyncio.sleep(2)
        
        if not await self.start_test_data():
            logger.warning("测试数据生成器启动失败，但继续运行")
        
        self.running = True
        logger.info("=== 所有服务启动完成 ===")
        logger.info("请访问 http://localhost:8081 查看GUI界面")
        logger.info("按 Ctrl+C 停止所有服务")
        
        return True
    
    async def stop_all(self):
        """停止所有服务。"""
        logger.info("=== 停止所有服务 ===")
        self.running = False
        
        # 停止测试进程
        if self.test_process and self.test_process.poll() is None:
            logger.info("停止测试数据生成器...")
            self.test_process.terminate()
            try:
                self.test_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.test_process.kill()
        
        # 停止GUI进程
        if self.gui_process and self.gui_process.poll() is None:
            logger.info("停止GUI服务...")
            self.gui_process.terminate()
            try:
                self.gui_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.gui_process.kill()
        
        # 停止主进程
        if self.main_process and self.main_process.poll() is None:
            logger.info("停止Beezer主服务...")
            self.main_process.terminate()
            try:
                self.main_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.main_process.kill()
        
        logger.info("=== 所有服务已停止 ===")
    
    async def run(self):
        """运行演示。"""
        # 设置信号处理
        def signal_handler(signum, frame):
            logger.info("收到停止信号，正在关闭...")
            asyncio.create_task(self.stop_all())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # 启动所有服务
        if not await self.start_all():
            return
        
        # 保持运行
        try:
            while self.running:
                await asyncio.sleep(1)
                
                # 检查进程状态
                if self.main_process and self.main_process.poll() is not None:
                    logger.error("Beezer主服务意外退出")
                    break
                
                if self.gui_process and self.gui_process.poll() is not None:
                    logger.error("GUI服务意外退出")
                    break
        
        except KeyboardInterrupt:
            logger.info("收到键盘中断信号")
        
        finally:
            await self.stop_all()


async def main():
    """主函数。"""
    demo = MonitorDemo()
    await demo.run()


if __name__ == "__main__":
    print("=== Beezer 数据监控演示 ===")
    print("这个演示将启动:")
    print("1. Beezer 主服务 (http://localhost:8000)")
    print("2. GUI 界面 (http://localhost:8081)")
    print("3. 测试数据生成器")
    print()
    print("请确保端口 8000 和 8081 没有被占用")
    print("按 Enter 继续，或 Ctrl+C 取消...")
    
    try:
        input()
    except KeyboardInterrupt:
        print("\n已取消")
        sys.exit(0)
    
    asyncio.run(main())
